package cn.loveapp.logistics.common.dao.es;

import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.config.elasticsearch.ElasticsearchConfiguration;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsQueryDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.LogisticsAbnormalStrategyChain;
import cn.loveapp.logistics.common.utils.ElasticsearchUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.base.CaseFormat;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchType;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.*;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 物流Es搜索Dao
 *
 * <AUTHOR>
 * @Date 2023/6/20 10:56
 */
@Repository
public class LogisticsOrderInfoSearchEsDao extends BaseElasticsearchDao<LogisticsOrderInfoSearchES> {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsOrderInfoSearchEsDao.class);

    protected static final String[] FIELDS = {EsFields.outSid};

    @Autowired
    private LogisticsAbnormalStrategyChain logisticsAbnormalStrategyChain;

    public LogisticsOrderInfoSearchEsDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations, RestHighLevelClient client, ResultsMapper mapper) {
        super(configuration, operations, client, mapper);
    }

    @Override
    public String getIndexName(LogisticsOrderInfoSearchES entity) {
        return operations.getPersistentEntityFor(LogisticsOrderInfoSearchES.class).getIndexName();
    }

    @Override
    public String getIndexName() {
        return operations.getPersistentEntityFor(LogisticsOrderInfoSearchES.class).getIndexName();
    }

    protected String[] getIndexName(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> indexNameArr = new HashSet<>();
        String indexName;
        TargetSellerInfo sellerInfo;
        for (int i = 0; i < sellerInfoList.size(); i++) {
            sellerInfo = sellerInfoList.get(i);
            LogisticsOrderInfoSearchES logisticsOrderInfoSearchES = new LogisticsOrderInfoSearchES();
            logisticsOrderInfoSearchES.setSellerId(sellerInfo.getTargetSellerId());
            indexName = getIndexName(logisticsOrderInfoSearchES);
            indexNameArr.add(indexName);
        }
        return indexNameArr.toArray(new String[0]);
    }

    protected String getRouting(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> routerArr = new HashSet<>();
        String router;
        TargetSellerInfo sellerInfo;
        for (int i = 0; i < sellerInfoList.size(); i++) {
            sellerInfo = sellerInfoList.get(i);
            router = sellerInfo.getTargetSellerId();
            if (StringUtils.isNotEmpty(router)) {
                routerArr.add(router);
            }
        }
        String join = StringUtils.join(routerArr, ",");
        return StringUtils.isEmpty(join) ? null : join;
    }

    /**
     * 更新ES记录没有就新增（为null的属性不修改）
     *
     * @param logisticsOrderInfoSearchES
     * @return
     */
    public int insertOrUpdate(LogisticsOrderInfoSearchES logisticsOrderInfoSearchES) {
        int result = updateByIdWithNotNull(logisticsOrderInfoSearchES);
        if (result <= 0) {
            LOGGER.logInfo(logisticsOrderInfoSearchES.getSellerId(), logisticsOrderInfoSearchES.getOutSid(), "ES 更新失败, 尝试插入: " + JSON.toJSONString(logisticsOrderInfoSearchES));
            save(logisticsOrderInfoSearchES);
            result = 1;
        }
        return result;
    }

    /**
     * 根据条件计数
     *
     * @param searchES
     * @param queryCondition
     * @return
     */
    public long countByQuery(LogisticsOrderInfoSearchES searchES, BoolQueryBuilder queryCondition) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
            .withSearchType(SearchType.QUERY_THEN_FETCH);
        queryCondition.mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true));
        queryCondition.must(termQuery(EsFields.sellerId, searchES.getSellerId()));
        queryCondition.must(termQuery(EsFields.storeId, searchES.getStoreId()));
        appendAppNameQuery(queryCondition, searchES.getAppName(), searchES.getStoreId());
        builder.withQuery(filterQuery(queryCondition));
        return count(searchES, builder.build());
    }

    public QueryBuilder filterQuery(QueryBuilder queryBuilder) {
        if (elasticsearchConfiguration.isFilterEnable()) {
            return boolQuery().filter(queryBuilder);
        } else {
            return queryBuilder;
        }
    }

    @Override
    public String getRouting(LogisticsOrderInfoSearchES entity) {
        return entity.getSellerId();
    }

    /**
     * 根据条件分页查询物流es索引
     *
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    public SearchResultDTO<LogisticsOrderInfoSearchES> logisticsListGetQueryByLimit(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        NativeSearchQueryBuilder builder = generateCommonQueryIndexAndRoute(userInfoDTO);

        List<FieldSortBuilder> fieldSortBuilders = new ArrayList<>();
        if (StringUtils.isEmpty(logisticsQuery.getSortField())) {
            builder.withSort(SortBuilders.fieldSort(EsFields.created).order(SortOrder.DESC));
            fieldSortBuilders.add(SortBuilders.fieldSort(EsFields.created).order(SortOrder.DESC));
        } else {
            String sortField = logisticsQuery.getSortField().contains("_")
                ? CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, logisticsQuery.getSortField())
                : logisticsQuery.getSortField();

            if (LogisticsQueryDTO.SORT_FIELD_CONSIGN_TIME.equals(sortField)) {
                sortField = EsFields.consignTime;
            }
            String sortDirection = logisticsQuery.getSortDirection();
            FieldSortBuilder sortBuilder;
            if (StringUtils.isEmpty(sortDirection)) {
                sortBuilder = SortBuilders.fieldSort(sortField).order(SortOrder.DESC);
            } else {
                sortBuilder = SortBuilders.fieldSort(sortField).order(SortOrder.fromString(sortDirection.toLowerCase()));
            }
            builder.withSort(sortBuilder);
            fieldSortBuilders.add(sortBuilder);
        }

        BoolQueryBuilder queryCondition = createBoolQueryCondition(logisticsQuery, userInfoDTO);
        builder.withQuery(queryCondition);
        SearchResultDTO<LogisticsOrderInfoSearchES> searchAfterResultDTO;
        if (logisticsQuery.getLastSearchSortValues() != null) {
            searchAfterResultDTO = searchAfterQuery(logisticsQuery, userInfoDTO, queryCondition, fieldSortBuilders);
        } else {
            searchAfterResultDTO = pageQuery(logisticsQuery, builder);
        }

        return searchAfterResultDTO;
    }

    /**
     * 生成ES查询条件
     *
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    private BoolQueryBuilder createBoolQueryCondition(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.mustNot(QueryBuilders.termsQuery(EsFields.isDeleted, true));

        if (BooleanUtils.isTrue(logisticsQuery.getIsExcludeProcessed())) {
            // 已处理不显示
            queryCondition.mustNot(QueryBuilders.termsQuery(EsFields.logisticsAbnormalInfoProcessStatus, AbnormalProcessStatus.PROCESSED));
        }

        if (CollectionUtils.isNotEmpty(userInfoDTO.getTargetSellerList())) {
            BoolQueryBuilder should = new BoolQueryBuilder();
            for (TargetSellerInfo sellerInfo :  userInfoDTO.getTargetSellerList()) {
                String targetStoreId = sellerInfo.getTargetStoreId();
                BoolQueryBuilder must = boolQuery().must(termQuery(EsFields.storeId, targetStoreId));
                if (StringUtils.isNotEmpty(sellerInfo.getTargetSellerId())) {
                    must.must(termQuery(EsFields.sellerId, sellerInfo.getTargetSellerId()));
                }
                appendAppNameQuery(must, sellerInfo.getTargetAppName(), sellerInfo.getTargetStoreId());
                should.should(must);
            }
            queryCondition.must(should);
        } else {
            if (StringUtils.isNotEmpty(userInfoDTO.getSellerId())) {
                queryCondition.must(termQuery(EsFields.sellerId, userInfoDTO.getSellerId()));
            }
            queryCondition.must(termQuery(EsFields.storeId, userInfoDTO.getStoreId()));
            appendAppNameQuery(queryCondition, userInfoDTO.getAppName(), userInfoDTO.getStoreId());
        }

        if (logisticsQuery.getStartTime() != null) {
            queryCondition.must(rangeQuery(EsFields.created).gte(minuteSecondFormatter.format(logisticsQuery.getStartTime())));
        }
        if (logisticsQuery.getEndTime() != null) {
            queryCondition.must(rangeQuery(EsFields.created).lte(minuteSecondFormatter.format(logisticsQuery.getEndTime())));
        }

        if (logisticsQuery.getStartConsignTime() != null) {
            queryCondition.must(rangeQuery(EsFields.consignTime).gte(minuteSecondFormatter.format(logisticsQuery.getStartConsignTime())));
        }

        if (logisticsQuery.getEndConsignTime() != null) {
            queryCondition.must(rangeQuery(EsFields.consignTime).lte(minuteSecondFormatter.format(logisticsQuery.getEndConsignTime())));
        }

        String lastAction = logisticsQuery.getLastAction();
        if (StringUtils.isNotEmpty(lastAction)) {
            if (AyLogisticsStatus.AWAITING_PICKUP.name().equals(lastAction) || AyLogisticsStatus.AWAITING_PICKUP.value().equals(lastAction)) {
                BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
                // 最新action为待揽件或者为空都视为待揽件
                boolQuery.should(termQuery(EsFields.lastAction, AyLogisticsStatus.AWAITING_PICKUP.value()))
                    .should(QueryBuilders.boolQuery().mustNot(existsQuery(EsFields.lastAction)))
                    .minimumShouldMatch(1);
                queryCondition.must(boolQuery);
            } else {
                List<String> statusList = AyLogisticsStatus.getStatusGroupList(lastAction);
                if (Objects.equals(AyLogisticsStatus.OTHER.name(), lastAction)
                    && CollectionUtils.isNotEmpty(statusList)) {
                    queryCondition.mustNot(termsQuery(EsFields.lastAction, statusList));
                    queryCondition.must(existsQuery(EsFields.lastAction));
                } else {
                    if (CollectionUtils.isNotEmpty(statusList)) {
                        queryCondition.must(termsQuery(EsFields.lastAction, statusList));
                    } else {
                        // 兼容线上直接传状态值
                        queryCondition.must(termQuery(EsFields.lastAction, lastAction));
                    }
                }
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getTid())) {
            queryCondition.must(termQuery(EsFields.businessInfoTIdList, logisticsQuery.getTid()));
        }

        // 查询异常相关
        if (BooleanUtils.isTrue(logisticsQuery.getOnlySearchNormal())) {
            queryCondition.must(generateNormalBoolQueryOfTradeApp());
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getAbnormalTypes()) || !Objects.isNull(logisticsQuery.getOnlySearchAbnormal())) {
            AbnormalQueryDTO abnormalQueryDTO = new AbnormalQueryDTO();
            abnormalQueryDTO.setOnlySearchAbnormal(logisticsQuery.getOnlySearchAbnormal());
            abnormalQueryDTO.setIsExcludeProcessed(logisticsQuery.getIsExcludeProcessed());
            abnormalQueryDTO.setIsExcludeOtherAbnormalOfTradeApp(logisticsQuery.getIsExcludeOtherAbnormalOfTradeApp());
            abnormalQueryDTO.setIsExistConsignTime(logisticsQuery.getIsExistConsignTime());
            BoolQueryBuilder builder = logisticsAbnormalStrategyChain.generalAbnormalBoolQueryChain(logisticsQuery.getAbnormalTypes(), abnormalQueryDTO);
            if (!Objects.isNull(builder)) {
                queryCondition.must(builder.minimumShouldMatch(1));
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getOutSid())) {
            queryCondition.must(termQuery(EsFields.outSid, logisticsQuery.getOutSid()));
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getOutSidList())) {
            queryCondition.must(termsQuery(EsFields.outSid, logisticsQuery.getOutSidList()));
        }
        // 包裹类型
        if (!Objects.isNull(logisticsQuery.getPackType())) {
            switch (logisticsQuery.getPackType()) {
                case NORMAL_DELIVERY:
                    // 正常发货件
                    queryCondition.mustNot(termQuery(EsFields.isTagIntercepted, true));
                    queryCondition.mustNot(existsQuery(EsFields.businessInfoRefundIdList));
                    break;
                case MERCHANT_INTERCEPT:
                    // 商家拦截件
                    queryCondition.must(termQuery(EsFields.isTagIntercepted, true));
                    break;
                case BUYER_RETURN:
                    queryCondition.must(existsQuery(EsFields.businessInfoRefundIdList));
                    break;
            }
        }

        // 物流公司
        if (StringUtils.isNotEmpty(logisticsQuery.getLogisticsCompany())) {
            queryCondition.must(boolQuery().should(termQuery(EsFields.companyCode, logisticsQuery.getLogisticsCompany()))
                .should(matchPhraseQuery(EsFields.companyName, logisticsQuery.getLogisticsCompany())));
        } else if (CollectionUtils.isNotEmpty(logisticsQuery.getLogisticsCompanyList())) {
            BoolQueryBuilder builder = boolQuery();
            for (String company : logisticsQuery.getLogisticsCompanyList()) {
                builder.should(boolQuery().should(termQuery(EsFields.companyCode, company))
                    .should(matchPhraseQuery(EsFields.companyName, company)).minimumShouldMatch(1));
            }
            queryCondition.must(builder.minimumShouldMatch(1));
        }

        if (CollectionUtils.isNotEmpty(logisticsQuery.getProcessStatus())) {
            if (logisticsQuery.getProcessStatus().contains(AbnormalProcessStatus.PENDING.value())) {
                queryCondition.must(boolQuery()
                    .should(
                        termsQuery(EsFields.logisticsAbnormalInfoProcessStatus, logisticsQuery.getProcessStatus()))
                    .should(boolQuery().mustNot(existsQuery(EsFields.logisticsAbnormalInfoProcessStatus)))
                    .minimumShouldMatch(1));
            } else {
                queryCondition.must(
                    termsQuery(EsFields.logisticsAbnormalInfoProcessStatus, logisticsQuery.getProcessStatus()));
            }
        }

        // 买家openUid
        if (StringUtils.isNotEmpty(logisticsQuery.getBuyerOpenUid())) {
            queryCondition.must(termQuery(EsFields.buyerOpenUid, logisticsQuery.getBuyerOpenUid()));
        }

        if (logisticsQuery.getOrderSellerMemo() != null) {
            queryCondition.must(termsQuery(EsFields.businessInfoSellerMemo, logisticsQuery.getOrderSellerMemo()));
        }

        if (!Objects.isNull(logisticsQuery.getBuyerSellerMemoSearchType())) {
            switch (logisticsQuery.getBuyerSellerMemoSearchType()) {
                case none:
                    // 无留言且无备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoSellerMemo)))
                        .should(termsQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)));
                    queryCondition.must(boolQuery()
                        .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoBuyerMessage)))
                        .should(termsQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)));
                    queryCondition.mustNot(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0));
                    queryCondition.mustNot(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0));
                    break;
                case any:
                    // 有留言或者有备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoSellerMemo)).mustNot(termsQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)))
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoBuyerMessage)).mustNot(termsQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)))
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0)))
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0)))
                        .minimumShouldMatch(1));
                    break;
                case seller:
                    // 有备注
                    queryCondition.must(boolQuery()
                        .should(boolQuery().must(existsQuery(EsFields.businessInfoSellerMemo)).mustNot(termsQuery(EsFields.businessInfoSellerMemoKeyword, StringUtils.EMPTY)))
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoOrderAyCustomFlag).gt(0)))
                        .should(boolQuery().must(rangeQuery(EsFields.businessInfoSellerFlagList).gt(0)))
                        .minimumShouldMatch(1));
                    break;
                case buyer:
                    // 有留言
                    queryCondition.must(boolQuery().must(existsQuery(EsFields.businessInfoBuyerMessage))
                        .mustNot(termsQuery(EsFields.businessInfoBuyerMessageKeyword, StringUtils.EMPTY)));
                    break;
            }
        }

        if (logisticsQuery.getIsRefund() != null) {
            if (BooleanUtils.isTrue(logisticsQuery.getIsRefund())) {
                queryCondition.must(termQuery(EsFields.businessInfoIsRefund, true));
            } else {
                queryCondition.must(boolQuery().should(termQuery(EsFields.businessInfoIsRefund, false))
                    .should(boolQuery().mustNot(existsQuery(EsFields.businessInfoIsRefund))));
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getSkuName())) {
            queryCondition.must(matchPhraseQuery(EsFields.businessInfoSkuNameList,
                ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getSkuName())));
        }

        if (CollectionUtils.isNotEmpty(logisticsQuery.getTidOrBuyerOpenUid())) {
            if (logisticsQuery.getTidOrBuyerOpenUid().size() == 1) {
                String tidOrBuyerOpenUid = logisticsQuery.getTidOrBuyerOpenUid().get(0);
                queryCondition.must(
                    boolQuery().should(termsQuery(EsFields.businessInfoTIdList, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQuery(EsFields.buyerOpenUid, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(matchPhraseQuery(EsFields.buyerNick, ElasticsearchUtil.splitAlphanumeric(tidOrBuyerOpenUid))));
            } else {
                queryCondition.must(
                    boolQuery().should(termsQuery(EsFields.businessInfoTIdList, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQuery(EsFields.buyerOpenUid, logisticsQuery.getTidOrBuyerOpenUid()))
                        .should(termsQuery(EsFields.buyerNickKeyword, logisticsQuery.getTidOrBuyerOpenUid())));
            }
        }

        handleSearchType(logisticsQuery, queryCondition);
        return queryCondition;
    }

    /**
     * 处理指定搜索类型的条件
     *
     * @param logisticsQuery
     * @param queryCondition
     */
    private static void handleSearchType(LogisticsQueryDTO logisticsQuery, BoolQueryBuilder queryCondition) {
        if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())
            || CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
            BoolQueryBuilder fuzzQueryBuilder = boolQuery();
            switch (logisticsQuery.getOrderFlagSearchType()) {
                case ACCURATE_SEARCH:
                    // 精确
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        queryCondition
                            .must(termsQuery(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        queryCondition.must(
                            termsQuery(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    break;
                case FUZZY_SEARCH:
                    // 模糊
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQuery(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQuery(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    queryCondition.must(fuzzQueryBuilder);

                    break;
                case REVERSE_FUZZY_SEARCH:
                    // 反向模糊
                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderSellerFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQuery(EsFields.businessInfoSellerFlagList, logisticsQuery.getOrderSellerFlagList()));
                    }

                    if (CollectionUtils.isNotEmpty(logisticsQuery.getOrderAyCustomFlagList())) {
                        fuzzQueryBuilder.should(
                            termsQuery(EsFields.businessInfoOrderAyCustomFlag, logisticsQuery.getOrderAyCustomFlagList()));
                    }
                    queryCondition.mustNot(fuzzQueryBuilder);
                    break;
                default:
            }
        }

        if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
            switch (logisticsQuery.getSkuInfoSearchType()) {
                case ACCURATE_SEARCH:
                    // 精确
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.must(termsQuery(EsFields.businessInfoOuterSkuIdListKeyword,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                case FUZZY_SEARCH:
                    // 模糊
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.must(matchPhraseQuery(EsFields.businessInfoOuterSkuIdList,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                case REVERSE_FUZZY_SEARCH:
                    // 反向模糊
                    if (StringUtils.isNotEmpty(logisticsQuery.getOuterSkuId())) {
                        queryCondition.mustNot(matchPhraseQuery(EsFields.businessInfoOuterSkuIdList,
                            ElasticsearchUtil.splitAlphanumeric(logisticsQuery.getOuterSkuId())));
                    }

                    break;
                default:
            }
        }
    }

    private QueryBuilder generateNormalBoolQueryOfTradeApp() {
        BoolQueryBuilder normalBoolQueryBuilder = boolQuery();

        // 仅排除存在的交易的异常
        normalBoolQueryBuilder.mustNot(termQuery(EsFields.notPickedUpAfterSendIsExists, true))
            .mustNot(termQuery(EsFields.firstTraceAfterPickedUpTimeoutIsExists, true))
            .mustNot(termQuery(EsFields.transferTimeoutIsExists, true))
            .mustNot(termQuery(EsFields.deliverySignTimeOutIsExists, true))
            .mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));
        return normalBoolQueryBuilder;
    }


    /**
     * 根据条件获取数量
     * @param logisticsQuery
     * @param userInfoDTO
     * @return
     */
    public Integer queryCountFromDB(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO) {
        BoolQueryBuilder queryCondition = createBoolQueryCondition(logisticsQuery, userInfoDTO);
        NativeSearchQueryBuilder builder = generateCommonQueryIndexAndRoute(userInfoDTO);
        builder.withQuery(filterQuery(queryCondition));
        return (int) count(
            LogisticsOrderInfoSearchES.of(userInfoDTO.getSellerId(), userInfoDTO.getStoreId(), userInfoDTO.getAppName()), userInfoDTO.getTargetSellerList(),
            builder.build());
    }

    private SearchResultDTO<LogisticsOrderInfoSearchES> pageQuery(LogisticsQueryDTO logisticsQuery, NativeSearchQueryBuilder builder) {
        builder.withPageable(PageRequest.of(logisticsQuery.getStart(), logisticsQuery.getLimit()));
        Page<LogisticsOrderInfoSearchES> result = operations.queryForPage(builder.build(), LogisticsOrderInfoSearchES.class);
        SearchResultDTO<LogisticsOrderInfoSearchES> searchAfterResultDTO = new SearchResultDTO<>();
        searchAfterResultDTO.setSearchResults(result.getContent());
        searchAfterResultDTO.setTotalResults((int) result.getTotalElements());
        return searchAfterResultDTO;
    }


    private NativeSearchQueryBuilder generateCommonQueryIndexAndRoute(UserInfoDTO userInfoDTO) {
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
            .withSearchType(SearchType.QUERY_THEN_FETCH);
        if (FIELDS != null) {
            builder.withFields(FIELDS);
        }
        builder.withIndices(getIndexName(new LogisticsOrderInfoSearchES()));
        builder.withRoute(userInfoDTO.getSellerId());
        if (CollectionUtils.isNotEmpty(userInfoDTO.getTargetSellerList())) {
            List<TargetSellerInfo> targetSellerInfoList = userInfoDTO.getTargetSellerList();
            if (CollectionUtils.isNotEmpty(targetSellerInfoList)) {
                //索引
                String[] indexNameArr = getIndexName(targetSellerInfoList);
                builder.withIndices(indexNameArr);
                //router
                String routingArr = getRouting(targetSellerInfoList);
                builder.withRoute(routingArr);
            }
        }
        return builder;
    }

    protected BoolQueryBuilder appendAppNameQuery(BoolQueryBuilder boolQueryBuilder, String appName, String storeId) {
        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId) && StringUtils.isNotEmpty(appName)) {
            // 淘宝特殊处理
            return boolQueryBuilder.must(boolQuery().should(termQuery(EsFields.appName, appName))
                .should(boolQuery().mustNot(QueryBuilders.existsQuery(EsFields.appName))).minimumShouldMatch(1));
        } else if (StringUtils.isNotEmpty(appName)) {
            return boolQueryBuilder.must(QueryBuilders.termQuery(EsFields.appName, appName));
        } else {
            return boolQueryBuilder.mustNot(QueryBuilders.existsQuery(EsFields.appName));
        }
    }

    /**
     *
     * searchafter查询
     *
     * @param logisticsQuery
     * @param userInfoDTO
     * @param queryCondition
     * @param fieldSortList
     * @return
     */
    private SearchResultDTO<LogisticsOrderInfoSearchES> searchAfterQuery(LogisticsQueryDTO logisticsQuery, UserInfoDTO userInfoDTO, BoolQueryBuilder queryCondition, List<FieldSortBuilder> fieldSortList) {
        SearchResultDTO<LogisticsOrderInfoSearchES> searchAfterResultDTO = new SearchResultDTO<>();
        try {
            if (CollectionUtils.isEmpty(fieldSortList)) {
                fieldSortList = new ArrayList<>();
                fieldSortList.add(SortBuilders.fieldSort(EsFields.gmtCreate));
            }
            //添加唯一标识，解决search after重复的问题
            fieldSortList.add(SortBuilders.fieldSort(EsFields.id).order(SortOrder.DESC));
            SearchSourceBuilder searchSourceBuilder = createSearchAfterSourceBuilder(queryCondition,
                FIELDS,
                null,
                logisticsQuery.getLimit(),
                fieldSortList.toArray(new FieldSortBuilder[fieldSortList.size()]));

            SearchRequest request = aiyongListSearchRequestCommonBuilder(userInfoDTO,
                SearchType.QUERY_THEN_FETCH,
                searchSourceBuilder);
            //里面有值则插入上一次的游标值,第一次访问时，lastSearchSortValues = [null]
            Object[] lastSearchSortValues = logisticsQuery.getLastSearchSortValues();
            if (lastSearchSortValues != null) {
                if (lastSearchSortValues.length != 0 && lastSearchSortValues[0] != null) {
                    searchSourceBuilder.searchAfter(lastSearchSortValues);
                    request.source(searchSourceBuilder);
                }
            }
            searchAfterResultDTO = searchAfter(request, searchSourceBuilder, LogisticsOrderInfoSearchES.class);
        } catch (Exception e) {
            LOGGER.logError(userInfoDTO.getNick(), "-", "searchAfter遍历出现异常: " + e.getMessage(), e);
        }

        return searchAfterResultDTO;
    }

    /**
     * 【支持多店】拼接es单店铺和多店铺通用查询索引、router条件
     *
     * @param userInfoDTO
     * @param searchType
     * @param searchSourceBuilder
     * @return
     */
    public SearchRequest aiyongListSearchRequestCommonBuilder(UserInfoDTO userInfoDTO, SearchType searchType, SearchSourceBuilder searchSourceBuilder) {
        SearchRequest request = new SearchRequest();
        String corpId = userInfoDTO.getCorpId();
        String sellerId = userInfoDTO.getSellerId();
        request.indices(getIndexName());
        request.routing(getHashRoutingKey(sellerId, corpId));
        List<TargetSellerInfo> targetSellerList = userInfoDTO.getTargetSellerList();
        if (CollectionUtils.isNotEmpty(targetSellerList)) {
            request.routing(getRouting(targetSellerList));
        }
        request.searchType(searchType).source(searchSourceBuilder);
        return request;
    }

}

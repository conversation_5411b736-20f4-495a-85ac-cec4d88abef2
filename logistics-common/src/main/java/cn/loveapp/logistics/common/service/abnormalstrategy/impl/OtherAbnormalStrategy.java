package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.*;
import static org.elasticsearch.index.query.QueryBuilders.existsQuery;

/**
 * 物流异常策略 其他未分类异常 (多店)
 * <p>
 * LogisticsAbnormalType.OTHER_ABNORMAL
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:41
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class OtherAbnormalStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(OtherAbnormalStrategy.class);

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails otherAbnormal = getLogisticsAbnormalDetails(logisticsAbnormalInfo);

        if (Objects.isNull(otherAbnormal)) {
            otherAbnormal = new LogisticsOrderInfo.AbnormalDetails();
        }

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        // 已揽收
        boolean checkOtherAbnormal;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            boolean delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
            List<String> otherAbnormalStatus = logisticsStatusList.stream().filter(status -> status.startsWith("4") && !getExcludeList().contains(status)).collect(Collectors.toList());
            boolean hasAbnormal = CollectionUtils.isNotEmpty(otherAbnormalStatus);
            checkOtherAbnormal = !delivered && hasAbnormal;
        } else {
            checkOtherAbnormal = false;
        }

        boolean isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkOtherAbnormal, otherAbnormal, null);

        if (checkOtherAbnormal || isUpdate) {
            resultLog("执行策略：【其他未分类异常】，判断结果：" + checkOtherAbnormal + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            setOtherAbnormal(logisticsAbnormalInfo, otherAbnormal);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkOtherAbnormal, isUpdate);
        return new ExecuteResult(isUpdate, null);
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = getLogisticsAbnormalCountDTO(abnormalCountDTO);
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        setOtherAbnormalCount(abnormalCountDTO, countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {

        String otherAbnormalIsExists = getOtherAbnormalIsExists();
        String otherAbnormalProcessStatus = getOtherAbnormalProcessStatus();
        // 异常存在且未处理
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(otherAbnormalIsExists, true));
        if (LogisticsAbnormal.OTHER_ABNORMAL.value().equals(getAbnormalType())) {
            queryCondition.mustNot(termQuery(otherAbnormalProcessStatus, AbnormalProcessStatus.PROCESSED));
        } else if (LogisticsAbnormal.OTHER_ABNORMAL_Of_TRADE_APP.value().equals(getAbnormalType())) {
            queryCondition.must(existsQuery(EsFields.consignTime));
            appendQueryBuilder(abnormalQueryDTO, queryCondition);
        }

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(otherAbnormalProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(otherAbnormalProcessStatus))));
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails otherAbnormal = getLogisticsAbnormalDetails(logisticsAbnormalInfo);
        if (Objects.isNull(otherAbnormal)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(otherAbnormal.getProcessStatus(), newProcessStatus, appName)) {
            otherAbnormal.setProcessStatus(newProcessStatus);
            setOtherAbnormal(logisticsAbnormalInfo, otherAbnormal);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = getLogisticsAbnormalDetails(logisticsOrderInfo.getLogisticsAbnormalInfo());
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.OTHER_ABNORMAL.value();
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime checkTime) {
        return null;
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_OTHER_PROMPT, false, Boolean.class);
    }


    protected LogisticsOrderInfo.AbnormalDetails getLogisticsAbnormalDetails(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo) {
        return logisticsAbnormalInfo.getOtherAbnormal();
    }

    protected List<String> getExcludeList(){
        return AyLogisticsStatus.EXCLUDE_OTHER_ABNORMAL_LIST;
    }

    protected Integer getLogisticsAbnormalCountDTO(LogisticsAbnormalCountDTO abnormalCountDTO) {
        return abnormalCountDTO.getOtherAbnormalCount();
    }

    protected String getOtherAbnormalIsExists(){
        return EsFields.otherAbnormalIsExists;
    }

    protected String getOtherAbnormalProcessStatus(){
        return EsFields.otherAbnormalProcessStatus;
    }

    protected void setOtherAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer countAll) {
        abnormalCountDTO.setOtherAbnormalCount(countAll);
    }

    protected void setOtherAbnormal(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo, LogisticsOrderInfo.AbnormalDetails otherAbnormal) {
        logisticsAbnormalInfo.setOtherAbnormal(otherAbnormal);
    }

    protected void resultLog(String logInfo){
        LOGGER.logInfo(logInfo);
    }

}

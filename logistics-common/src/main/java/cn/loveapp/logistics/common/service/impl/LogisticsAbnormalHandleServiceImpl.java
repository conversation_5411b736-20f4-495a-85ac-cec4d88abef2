package cn.loveapp.logistics.common.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.constant.AbnormalCheckSendStatus;
import cn.loveapp.logistics.common.dao.es.LogisticsOrderInfoSearchEsDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.redis.LogisticsSaveLockRedisDao;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.ShopsNoticeRequest;
import cn.loveapp.logistics.common.dto.response.ShopsNoticeResponse;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.*;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.service.abnormalstrategy.LogisticsAbnormalStrategyChain;
import cn.loveapp.logistics.common.service.external.TradePcService;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import static cn.loveapp.logistics.common.constant.AbnormalCheckSendStatus.SENDING;
import static cn.loveapp.logistics.common.constant.AbnormalCheckSendStatus.SEND_END;

/**
 * <AUTHOR>
 * @Date 2023/6/26 15:30
 */
@Service
public class LogisticsAbnormalHandleServiceImpl implements LogisticsAbnormalHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalHandleService.class);

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsOrderInfoSearchEsDao logisticsOrderInfoSearchEsDao;

    @Autowired
    private LogisticsAbnormalStrategyChain logisticsAbnormalStrategyChain;

    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    @Autowired
    private LogisticsSaveLockRedisDao logisticsSaveLockRedisDao;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private TradePcService tradePcService;

    /**
     * erp异常物流上线时间
     */
    private static final LocalDateTime ERP_ABNORMAL_LOGISTICS_ONLINE_TIME = LocalDateTime.of(2025, 4, 3, 21, 0);

    @Override
    public boolean checkAbnormalLogistics(LogisticsHandleBo logisticsHandleBo, boolean checkAbnormal) {

        String outSid = logisticsHandleBo.getOutSid();
        String storeId = logisticsHandleBo.getStoreId();
        String sellerId = logisticsHandleBo.getSellerId();
        String appName = logisticsHandleBo.getAppName();

        LogisticsOrderInfo logisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
        if (Objects.isNull(logisticsOrderInfo) || SEND_END.equals(logisticsOrderInfo.getAbnormalCheckSendStatus())) {
            LOGGER.logError(sellerId, outSid, "运单物流单不存在或已结束，跳过,运单号：" + outSid);
            return false;
        }

        try {
            BeanUtils.copyProperties(logisticsOrderInfo, logisticsHandleBo);
            logisticsHandleBo.setLogisticsOrderInfo(logisticsOrderInfo);
            logisticsHandleBo.setCheckAbnormal(checkAbnormal);
            pullLogisticsAbnormalData(logisticsHandleBo);
        } catch (LogisticsHandlesException e) {
            LOGGER.logError(e.getMessage(), e);
            return false;
        }
        return true;
    }

    @Override
    public void sendAbnormalLogisticsNotify(LogisticsHandleBo logisticsHandleBo, List<String> abnormalTypeNotifyList) {
        String outSid = logisticsHandleBo.getOutSid();
        String storeId = logisticsHandleBo.getStoreId();
        String sellerId = logisticsHandleBo.getSellerId();
        String appName = logisticsHandleBo.getAppName();

        LogisticsOrderInfo logisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
        if (Objects.isNull(logisticsOrderInfo) || SEND_END.equals(logisticsOrderInfo.getAbnormalCheckSendStatus())) {
            LOGGER.logError(sellerId, outSid, "运单物流单不存在或已结束，跳过,运单号：" + outSid);
            return;
        }

        for (String abnormal : abnormalTypeNotifyList) {
            boolean needSendAbnormalNotify = logisticsAbnormalStrategyChain.checkNeedSendAbnormalNotify(logisticsOrderInfo, abnormal);
            if (needSendAbnormalNotify) {
                ShopsNoticeRequest request = new ShopsNoticeRequest();
                boolean generalSuccess = request.generalRequest(logisticsOrderInfo, abnormal);
                if (!generalSuccess) {
                    continue;
                }
                // 多店消息弹窗提醒
                LOGGER.logInfo("调用多店弹窗消息：request:" + JSON.toJSONString(request));
                CommonApiResponse<ShopsNoticeResponse> noticeShopMessage = tradePcService.handelPushNoticeShopMessage(request);
                LOGGER.logInfo("调用多店弹窗消息：response:" + JSON.toJSONString(noticeShopMessage));
            }
        }

    }

    @Override
    public LogisticsAbnormalCountDTO abnormalStatisticsGet(AbnormalQueryDTO abnormalQueryDTO, UserInfoDTO userInfoDTO) throws LogisticsHandlesException {
        // 查询各个异常指标的统计计数
        List<TargetSellerInfo> userInfoList = new ArrayList<>();

        if (userInfoDTO.getTargetSellerList() != null) {
            // 多平台查询
            userInfoList.addAll(userInfoDTO.getTargetSellerList());
        } else {
            TargetSellerInfo targetSellerInfo = new TargetSellerInfo();
            targetSellerInfo.setTargetSellerId(userInfoDTO.getSellerId());
            targetSellerInfo.setTargetAppName(userInfoDTO.getAppName());
            targetSellerInfo.setTargetStoreId(userInfoDTO.getStoreId());
            targetSellerInfo.setTargetNick(userInfoDTO.getNick());
            userInfoList.add(targetSellerInfo);
        }

        if (LogisticsUtil.isTradeERP(userInfoDTO.getAppName())) {
            // 限制erp 异常物流查询范围(上线时间开始计算)
            if (abnormalQueryDTO.getStartConsignTime() == null
                || abnormalQueryDTO.getStartConsignTime().isBefore(ERP_ABNORMAL_LOGISTICS_ONLINE_TIME)) {
                abnormalQueryDTO.setStartConsignTime(ERP_ABNORMAL_LOGISTICS_ONLINE_TIME);
            }
        }

        return logisticsAbnormalStrategyChain.countAbnormalChain(userInfoList, abnormalQueryDTO, logisticsOrderInfoSearchEsDao);
    }

    @Override
    public boolean pullLogisticsAbnormalData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException {

        if (Objects.isNull(logisticsHandleBo)) {
            LOGGER.logError("异常物流更新失败，参数为空");
            return false;
        }

        String outSid = logisticsHandleBo.getOutSid();
        String storeId = logisticsHandleBo.getStoreId();
        String sellerId = logisticsHandleBo.getSellerId();
        String appName = logisticsHandleBo.getAppName();
        boolean checkAbnormal = logisticsHandleBo.isCheckAbnormal();

        Boolean isOrderInfoFirstUpdate = logisticsHandleBo.getIsOrderInfoFirstUpdate();
        Boolean needSendCheckMsg = null;

        LogisticsOrderInfo logisticsOrderInfo = null;
        List<String> abnormalTypeNotifyList = new ArrayList<>();
        boolean isUpdate = false;
        // 运单锁
        String lockValue = logisticsSaveLockRedisDao.lockLogistics(outSid, sellerId, storeId, appName);
        try {
            logisticsOrderInfo = ConvertUtil.getOrDefault(logisticsHandleBo.getLogisticsOrderInfo(), () -> logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName));

            if (Objects.isNull(logisticsOrderInfo)) {
                LOGGER.logInfo(sellerId, outSid, "不存在需更新的物流单，跳过异常物流更新入库");
                return false;
            }

            // 兼容旧数据物流轨迹直接入库status为null的情况
            Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
            if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
                logisticsStatusList.remove(null);
            }
            logisticsHandleBo.setLogisticsOrderInfo(logisticsOrderInfo);

            // 物流单是否结束
            boolean sendEnd = logisticsOrderHandleService.checkLogisticsIsEnd(logisticsOrderInfo);
            // 判断是否需要发送异常物流校验消息 （不存在正在发送的消息 || 处理异常物流校验队列）
            needSendCheckMsg = AbnormalCheckSendStatus.checkIsNotSend(logisticsOrderInfo.getAbnormalCheckSendStatus()) || checkAbnormal;

            if (logisticsHandleBo.isCheckAbnormal()) {
                // 物流长时间未更新,会拉取api,发送物流轨迹消息,消费时会重新走到本方法,因此这里如果返回true直接退出方法
                if (logisticsOrderHandleService.checkOverdueUpdateAndPushTraceMessage(logisticsHandleBo)) {
                    return false;
                }
            }

            if (!SENDING.equals(logisticsOrderInfo.getAbnormalCheckSendStatus())) {
                logisticsOrderInfo.setAbnormalCheckSendStatus(SENDING);
                isUpdate = true;
            }
            if (sendEnd && !SEND_END.equals(logisticsOrderInfo.getAbnormalCheckSendStatus())) {
                logisticsOrderInfo.setAbnormalCheckSendStatus(SEND_END);
                isUpdate = true;
            }

            LocalDateTime sendDeadLine = null;
            try {
                ExecuteResult result = logisticsAbnormalStrategyChain.executeChain(logisticsOrderInfo, abnormalTypeNotifyList);
                sendDeadLine = result.getDeadline();
                if (!result.isChanged() && !isUpdate) {
                    return false;
                }
            } finally {
                if (BooleanUtils.isTrue(needSendCheckMsg) && !sendEnd) {
                    logisticsSendHandleService.pushLogisticsAbnormalResendMsg(logisticsOrderInfo, sendDeadLine);
                }
            }

            if (BooleanUtils.isTrue(isOrderInfoFirstUpdate)) {
                logisticsOrderInfoDao.insert(logisticsOrderInfo);
            } else {
                logisticsOrderInfoDao.update(logisticsOrderInfo);
            }

            // 存在异常物流
            LogisticsOrderInfoSearchES logisticsSearchES = LogisticsOrderInfoSearchES.of(logisticsOrderInfo);
            if (BooleanUtils.isTrue(logisticsConfig.getSaveAllEsEnable())) {
                // 全部保存es
                logisticsOrderInfoSearchEsDao.insertOrUpdate(logisticsSearchES);
            } else {
                // 只保留异常物流到es记录, 不存在异常则清除
                if (BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal())) {
                    logisticsOrderInfoSearchEsDao.insertOrUpdate(logisticsSearchES);
                } else {
                    logisticsOrderInfoSearchEsDao.deleteById(logisticsSearchES);
                }
            }

        } catch (Exception e) {
            LOGGER.logError("入库失败：" + e.getMessage(), e);
            throw new LogisticsHandlesException("入库失败：newLogisticsOrderInfo:" + JSON.toJSONString(logisticsOrderInfo), e);
        } finally {
            logisticsSaveLockRedisDao.unLockLogistics(outSid, sellerId, storeId, appName, lockValue);

            if (CollectionUtils.isNotEmpty(abnormalTypeNotifyList)) {
                // 发送异常弹窗消息
                logisticsSendHandleService.pushLogisticsAbnormalNotifyMsg(logisticsOrderInfo, abnormalTypeNotifyList);
            }
        }
        return true;
    }

    @Override
    public void setLogisticsProcessStatus(LogisticsOrderInfo logisticsOrderInfo, String newProcessStatus) {
        logisticsAbnormalStrategyChain.setLogisticsProcessStatus(logisticsOrderInfo, newProcessStatus);
    }
}

package cn.loveapp.logistics.common.dao.mongo;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.constant.MongoConstant;
import cn.loveapp.logistics.common.exception.MongoDBSaveException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.PropertyFilter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mongodb.bulk.BulkWriteResult;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.UpdateResult;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.BulkOperations.BulkMode;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;

import javax.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Supplier;

/**
 * BaseMongoDao
 *
 * @program:
 * @description: BaseMongoDao
 * @author: Jason
 * @create: 2020-02-17 19:41
 **/
abstract public class BaseMongoDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseMongoDao.class);

    protected final String primaryKey = "_id";

    protected MongoTemplate mongoTemplate;

    protected final String _gmtModified = "gmtModified";
    protected final String _gmtCreate = "gmtCreate";

    /**
     * 操作最大重试次数
     */
    @Value("${mongo.operation.maxRetryCount:5}")
    private int operationMaxRetryCount;

    private static Map<Class, List<Field>> classMap = new ConcurrentHashMap<>();

    protected final List<String> ignoreFields = Arrays.asList(
        primaryKey, _gmtCreate, _gmtModified,
        MongoConstant.OUT_SID_FIELD, MongoConstant.STORE_ID_FIELD, MongoConstant.SELLER_ID_FIELD,
        MongoConstant.APP_NAME_FIELD
    );

    abstract protected String getShardPrimaryKey();

    protected void addIgnoreFields(String field) {
        ignoreFields.add(field);
    }

    protected static final ObjectMapper OBJECT_MAPPER;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        OBJECT_MAPPER.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    public BaseMongoDao(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    abstract protected boolean isShardCollection();

    /**
     * 谨慎使用，此方法会将原document覆盖成object->o
     *
     * @param o
     * @param isAutoUpdateModified
     * @return
     */
    protected Update fromDocument(Object o, Boolean isAutoUpdateModified) {
        try {
            String document = OBJECT_MAPPER.writeValueAsString(o);
            Document d = Document.parse(document);
            if (d.containsKey(primaryKey) && !StringUtils.isEmpty((String) d.get(primaryKey))) {
                if (Boolean.TRUE.equals(isAutoUpdateModified)) {
                    return Update.fromDocument(d).currentDate(_gmtModified);
                } else {
                    return Update.fromDocument(d);
                }
            } else {
                Assert.isNull(o, "Document must _id is not null");
            }
        } catch (JsonProcessingException e) {
            LOGGER.logError("document parsing error");
        }
        return null;
    }

    /**
     * 通过object创建Update对象包括NULL值
     *
     * @param o
     * @return
     */
    protected Update fromObjectCreateUpdateWithNull(Object o) {
        try {
            Update update = getFieldValue(o, ignoreFields, Boolean.FALSE);
            update.currentDate(_gmtModified);
            return update;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 通过object创建Update对象包括NULL值
     *
     * @param o
     * @return
     * @throws
     */
    protected Update fromObjectCreateUpdateWithNull(Object o, List<String> ignoreFields) {
        try {
            Update update = getFieldValue(o, ignoreFields, Boolean.FALSE);
            update.currentDate(_gmtModified);
            return update;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 通过object创建Update对象
     *
     * @param o
     * @return
     * @throws
     */
    protected Update fromObjectCreateUpdate(Object o) {
        try {
            Update update = getFieldValue(o, ignoreFields, Boolean.TRUE);
            update.currentDate(_gmtModified);
            return update;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 通过object创建Update对象
     *
     * @param o
     * @param ignoreFields
     * @return
     * @throws
     */
    protected Update fromObjectCreateUpdate(Object o, List<String> ignoreFields) {
        try {
            Update update = getFieldValue(o, ignoreFields, Boolean.TRUE);
            update.currentDate(_gmtModified);
            return update;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 通过object和指定的字段创建Update对象
     *
     * @param o
     * @param fields
     * @return
     */
    protected Update fromNeedFieldsCreateUpdate(Object o, List<String> fields) {
        try {
            Update update = getFieldValue(o, Collections.emptyList(), Boolean.FALSE, fields);
            update.currentDate(_gmtModified);
            return update;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 此方法用于只更新需要的字段
     *
     * @param o
     * @param isAutoUpdateModified
     * @param needIgnoreFields     是否需要忽略部分字段
     * @return
     */
    protected Update fromNeedDocumentUpdateValues(Object o, Boolean isAutoUpdateModified, Boolean needIgnoreFields) {
        try {
            Update update = getFieldValue(o, needIgnoreFields ? ignoreFields : Collections.emptyList(), Boolean.TRUE);
            if (Boolean.TRUE.equals(isAutoUpdateModified)) {
                return update.currentDate(_gmtModified);
            } else {
                return update;
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.logError("反射结果异常, o => " + JSON.toJSONString(o) + ", exception error message =>" + e.getMessage(), e);
            throw new RuntimeException("function fromObjectWithNullUpdateValues throw reflection exception", e);
        }
    }

    /**
     * 生成需要更新的字段 需要忽略 ignoreFields
     *
     * @param o
     * @param isAutoUpdateModified
     * @return
     */
    protected Update fromNeedDocumentUpdateValues(Object o, Boolean isAutoUpdateModified) {
        return fromNeedDocumentUpdateValues(o, isAutoUpdateModified, true);
    }

    /**
     * 更新单条操作
     *
     * @param query
     * @param update
     * @return
     */
    protected int updateOperation(Query query, Update update) {
        return executeOperation(() -> updateFirst(query, update));
    }

    /**
     * 可选更新操作
     *
     * @param query
     * @param update
     * @param isUpdateMulti 是否需要批量更新
     * @return
     */
    protected int updateOperation(Query query, Update update, Boolean isUpdateMulti) {
        return executeOperation(() -> {
            if (Boolean.TRUE.equals(isUpdateMulti)) {
                return updateMulti(query, update);
            } else {
                return updateFirst(query, update);
            }
        });
    }

    private int updateFirst(Query query, Update update) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb update operation collection <" + getCollectionName() + "> build query is ," + JSON.toJSONString(query));
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb update operation collection <" + getCollectionName() + "> update values ," + JSON.toJSONString(update));
        }
        UpdateResult updateResult = mongoTemplate.updateFirst(query, update, getCollectionName());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb update operation collection <" + getCollectionName() + "> update results ," + JSON.toJSONString(updateResult));
        }
        return (int) updateResult.getMatchedCount();
    }

    private int updateMulti(Query query, Update update) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb updateMulti operation collection <" + getCollectionName() + "> build query is ," + JSON.toJSONString(query));
        }
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb updateMulti operation collection <" + getCollectionName() + "> update values ," + JSON.toJSONString(update));
        }
        UpdateResult updateResult = mongoTemplate.updateMulti(query, update, getCollectionName());
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb updateMulti operation collection <" + getCollectionName() + "> update results ," + JSON.toJSONString(updateResult));
        }
        return (int) updateResult.getMatchedCount();
    }

    protected <T> List<T> find(Query query, Class<T> t) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb find operation collection <" + getCollectionName() + "> build query is => " + JSON.toJSONString(query));
        }
        return executeOperation(() -> mongoTemplate.find(query, t, getCollectionName()));
    }

    protected <T> T findOne(Query query, Class<T> t) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb findOne operation collection <" + getCollectionName() + "> build query is => " + JSON.toJSONString(query));
        }
        return executeOperation(() -> mongoTemplate.findOne(query, t, getCollectionName()));
    }

    protected BulkOperations getBulkOps(BulkMode bulkMode) {
        return mongoTemplate.bulkOps(bulkMode, getCollectionName());
    }

    protected int bulkOperationsExecute(BulkOperations bulkOperations) {
        return executeOperation(() -> {
            BulkWriteResult bulkWriteResult = bulkOperations.execute();
            return bulkWriteResult.getMatchedCount();
        });
    }

    protected <T> T insert(T t) {
        return executeOperation(() -> mongoTemplate.insert(t, getCollectionName()));
    }

    protected <T> Collection<T> insertAll(Collection<? extends T> objectsToSave) {
        return executeOperation(() -> mongoTemplate.insertAll(objectsToSave));
    }

    protected <T> T save(T t) throws MongoDBSaveException {
        if (Boolean.TRUE.equals(isShardCollection())) {
            throw new MongoDBSaveException(8, "save命令不能运行在分片集群内");
        }
        return mongoTemplate.save(t);
    }

    protected Query buildQueryById(String pId) {
        return query(where(primaryKey).is(pId));
    }

    protected Query buildQueryById(String pId, String shardPId) {
        return query(where(primaryKey).is(pId).and(getShardPrimaryKey()).is(shardPId));
    }

    protected Query buildQueryById(List<String> pIds, List<String> shardPIds) {
        return query(where(primaryKey).in(pIds).and(getShardPrimaryKey()).in(shardPIds));
    }

    protected Update buildUpdate() {
        return new Update().currentDate(_gmtModified);
    }

    protected Query queryWithFields(List<String> fields, Criteria c) {
        Query query = new Query();
        fields.forEach(field -> query.fields().include(field));
        if (c != null) {
            query.addCriteria(c);
        }
        return query;
    }

    protected Query queryWithOutFields(List<String> fields, Criteria c) {
        Query query = new Query();
        fields.forEach(field -> query.fields().exclude(field));
        if (c != null) {
            query.addCriteria(c);
        }
        return query;
    }

    protected Query query(Criteria c) {
        return new Query().addCriteria(c);
    }

    protected Criteria where(String s) {
        return Criteria.where(s);
    }

    protected Criteria appendAppNameCriteria(Criteria criteria, String appName) {
        return criteria.and(MongoConstant.APP_NAME_FIELD).is(StringUtils.trimToNull(appName));
    }

    protected Query setFields(@NotNull Query query, List<String> fields) {
        fields.forEach(field -> query.fields().include(field));
        return query;
    }

    protected Query setFields(@NotNull Query query, @NotNull String field) {
        query.fields().include(field);
        return query;
    }

    protected int remove(Query query) {
        return executeOperation(() -> {
            if (LOGGER.isDebugEnabled()) {
                LOGGER.logDebug("mongodb remove operation collection <" + getCollectionName() + "> build query is ," + JSON.toJSONString(query));
            }
            DeleteResult deleteResult = mongoTemplate.remove(query, getCollectionName());
            if (LOGGER.isDebugEnabled()) {
                LOGGER.logDebug("mongodb remove operation collection <" + getCollectionName() + "> build query is ," + JSON.toJSONString(deleteResult));
            }
            return (int) deleteResult.getDeletedCount();
        });
    }

    /**
     * 获取collectionName
     *
     * @return string
     */
    abstract protected String getCollectionName();

    protected PropertyFilter getPropertyFilter() {
        return new PropertyFilter() {
            /**
             * @param object the owner of the property
             * @param name the name of the property
             * @param value the value of the property
             * @return true if the property will be included, false if to be filtered out
             */
            @Override
            public boolean apply(Object object, String name, Object value) {
                if (value instanceof LocalDateTime) {
                    return false;
                }
                return true;
            }
        };
    }

    private Update getFieldValue(Object object, @NotNull List<String> ignoreFields, @NotNull Boolean isIgnoreNull) throws NoSuchFieldException, IllegalAccessException {
        return getFieldValue(object, ignoreFields, isIgnoreNull, null);
    }

    /**
     * 获取本类及其父类的字段属性,并对属性取消访问权限检查
     *
     * @param clazz 当前类对象
     * @return 字段数组
     */
    private static List<Field> getAllFields(Class<?> clazz) throws NoSuchFieldException {
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null) {
            for (Field f : new ArrayList<>(Arrays.asList(clazz.getDeclaredFields()))) {
                if (Modifier.isStatic(f.getModifiers())) {
                    continue;
                }
                f.setAccessible(true);
                fieldList.add(f);
            }
            clazz = clazz.getSuperclass();
        }
        return fieldList;
    }

    /**
     * 通过反射获取Update属性
     *
     * @param object       类对象
     * @param ignoreFields 需要忽略的字段名称
     * @param isIgnoreNull 是否忽略Null值字段
     * @param needFields   指定更新的字段列表  (传null 表示不指定字段更新)
     * @return org.springframework.data.mongodb.core.query.Update
     * @throws NoSuchFieldException
     * @throws IllegalAccessException
     */
    private Update getFieldValue(Object object, @NotNull List<String> ignoreFields, @NotNull Boolean isIgnoreNull, List<String> needFields) throws NoSuchFieldException, IllegalAccessException {
        Update update = new Update();
        Class<?> cls = object.getClass();
        List<Field> fieldList = new ArrayList<>();
        if (classMap.containsKey(cls)) {
            fieldList = classMap.get(cls);
        } else {
            List<Field> allFields = getAllFields(cls);
            if (allFields.size() == 0) {
                throw new IllegalArgumentException(object.getClass() + "未获取到属性!");
            }
            fieldList.addAll(allFields);
            classMap.put(cls, fieldList);
        }

        for (Field field : fieldList) {
            Object value = field.get(object);
            if (Boolean.TRUE.equals(isIgnoreNull) && Objects.isNull(value)) {
                continue;
            }
            String fieldName = field.getName();
            if (ignoreFields.size() > 0 && ignoreFields.contains(fieldName)) {
                continue;
            }
            if (CollectionUtils.isNotEmpty(needFields) && !needFields.contains(fieldName)) {
                continue;
            }
            update.set(fieldName, value);
        }
        return update;
    }

    private <T> T executeOperation(Supplier<T> supplier) {
        int retryCount = 0;
        while (true) {
            try {
                return supplier.get();
            } catch (DuplicateKeyException e) {
                throw e;
            } catch (Exception e) {
                LOGGER.logError("mongo operation error , retryCount => " + retryCount, e);
                retryCount++;
                if (retryCount > operationMaxRetryCount) {
                    LOGGER.logError("mongo operation error , retry Max", e);
                    throw e;
                }
            }
            try {
                Thread.sleep(200 * retryCount);
            } catch (Exception e) {
            }
        }
    }

    /**
     * 根据查询条件统计数量
     *
     * @param query
     * @param t
     * @param <T>
     * @return
     */
    protected <T> Long count(Query query, Class<T> t) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug("mongodb count operation collection <" + getCollectionName() + "> build query is => "
                + JSON.toJSONString(query));
        }
        return executeOperation(() -> mongoTemplate.count(query, t, getCollectionName()));
    }

}

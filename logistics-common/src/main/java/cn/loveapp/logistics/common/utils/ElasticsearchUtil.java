package cn.loveapp.logistics.common.utils;

import cn.loveapp.common.utils.LoggerHelper;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.cluster.routing.Murmur3HashFunction;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.regex.Pattern;

/**
 * ESUtil
 *
 * <AUTHOR>
 * @date 2020/2/22
 */
public class ElasticsearchUtil {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ElasticsearchUtil.class);

    /**
     * 数字字母正则
     */
    public static final Pattern ALPHANUMERIC_PATTERN = Pattern.compile("([\\w|\\d])");

    /**
     * 匹配字母a
     */
    public static final Pattern A_PATTERN = Pattern.compile("(a)", Pattern.CASE_INSENSITIVE);

    public static final Pattern ALL_PATTERN = Pattern.compile("(.)");

    public static <E> ArrayList<E> toList(E ob) {
        if (ob == null) {
            return null;
        }
        return Lists.newArrayList(ob);
    }

    public static <E> ArrayList<E> toList(Collection<E> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return Lists.newArrayList(cl);
    }

    public static String toString(Collection<String> cl) {
        if (CollectionUtils.isEmpty(cl)) {
            return null;
        }
        return String.join(",", cl);
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static void splitAlphanumeric(List<String> texts) {
        if (CollectionUtils.isNotEmpty(texts)) {
            try {
                for (int i = 0; i < texts.size(); i++) {
                    String text = texts.get(i);
                    if (StringUtils.isEmpty(text)) {
                        continue;
                    }
                    texts.set(i, A_PATTERN.matcher(ALPHANUMERIC_PATTERN.matcher(text).replaceAll(" $1 ")).replaceAll("$1$1"));
                }
            } catch (Exception e) {
                LOGGER.logError(e.getMessage(), e);
            }
        }
    }

    /**
     * 在数字和字母离两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static String splitAlphanumeric(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            return A_PATTERN.matcher(ALPHANUMERIC_PATTERN.matcher(text).replaceAll(" $1 ")).replaceAll("$1$1");
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }

    /**
     * 在所有字符两边添加空格, 防止分词无法查询不规整的数字字母
     * 字母A替换为AA, 防止单个字母A被分词忽略
     *
     * @return
     */
    public static String splitAll(String text) {
        if (StringUtils.isEmpty(text)) {
            return text;
        }
        try {
            return A_PATTERN.matcher(ALL_PATTERN.matcher(text.toLowerCase()).replaceAll("$1 ")).replaceAll("$1$1");
        } catch (Exception e) {
            LOGGER.logError(e.getMessage(), e);
            return text;
        }
    }

    /**
     * 获取router所在的起始shardId
     *
     * @param routing
     * @return
     */
    public static Integer shardId(String routing) {
        return shardId(StringUtils.EMPTY, routing, 4, 30, 30);
    }

    /**
     * 获取数据所在的shardId (id为空时, 获取起始shardId)
     *
     * @param id
     * @param routing
     * @param routingPartitionSize
     * @param routingNumShards
     * @param numberOfShards
     * @return
     */
    public static Integer shardId(String id, String routing, int routingPartitionSize, int routingNumShards, int numberOfShards) {
        try {
            if (routing == null) {
                return null;
            }
            int routingFactor = routingNumShards / numberOfShards;
            final String effectiveRouting;
            final int partitionOffset;

            effectiveRouting = routing;

            partitionOffset = Math.floorMod(Murmur3HashFunction.hash(StringUtils.trimToEmpty(id)), routingPartitionSize);

            return calculateScaledShardId(effectiveRouting, partitionOffset, routingNumShards, routingFactor);
        } catch (Exception e) {
            LOGGER.logWarn(routing + " 获取shardId失败: " + e.getMessage());
            return null;
        }
    }

    private static int calculateScaledShardId(String effectiveRouting, int partitionOffset, int routingNumShards,
                                              int routingFactor) {
        final int hash = Murmur3HashFunction.hash(effectiveRouting) + partitionOffset;

        // we don't use IMD#getNumberOfShards since the index might have been shrunk such that we need to use the size
        // of original index to hash documents
        return Math.floorMod(hash, routingNumShards) / routingFactor;
    }
}

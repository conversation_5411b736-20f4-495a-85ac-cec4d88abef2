package cn.loveapp.logistics.common.dto;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.logistics.api.constant.BusinessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 物流api查询DTO
 *
 * <AUTHOR>
 * @Date 2023/6/5 18:32
 */
@Data
@ApiModel
public class LogisticsInfoApiSearchDTO {

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    private String outSid;

    /**
     * 物流公司Code
     */
    @ApiModelProperty(value = "物流公司Code", required = false)
    private String logisticsCompanyCode;

    /**
     * 寄件人or收件人 手机号后四位,只有顺丰订阅快递鸟时才需要
     */
    @ApiModelProperty(value = "寄件人or收件人 手机号后四位,只有顺丰订阅快递鸟时才需要", required = false)
    private String customerName;

    /**
     * 业务类型：
     */
    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull
    private BusinessType businessType;

    /**
     * 业务类型id（根据业务类型区分）
     */
    @ApiModelProperty(value = "业务类型id（根据业务类型区分）", required = true)
    @NotEmpty
    private List<String> businessIds;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull
    private String sellerId;

    /**
     * 用户nick
     */
    @ApiModelProperty(value = "用户nick", required = false)
    private String sellerNick;

    /**
     * 用户平台
     */
    @ApiModelProperty(value = "用户平台", required = true)
    @NotNull
    private String storeId;

    /**
     * 应用
     */
    @ApiModelProperty(value = "应用", required = true)
    @NotNull
    private String appName;

    /**
     * 源物流公司（电商平台对应物流公司code/name/id）
     */
    @ApiModelProperty(value = "源物流公司（电商平台对应物流公司code/name/id）")
    private String sourceLogisticsCompany;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序规则")
    private String sort = "desc";

    /**
     * 物流平台
     */
    @ApiModelProperty(value = "物流平台")
    private String logisticsStoreId = CommonLogisticsConstants.PLATFORM_KDNIAO;

    /**
     * 物流平台
     */
    @ApiModelProperty(value = "物流appName")
    private String logisticsAppName = CommonAppConstants.APP_LOGISTICS;

    /**
     * 收件人/寄件人手机 顺丰必传
     */
    @ApiModelProperty(value = "手机号（顺丰必传）")
    private String phone;

}

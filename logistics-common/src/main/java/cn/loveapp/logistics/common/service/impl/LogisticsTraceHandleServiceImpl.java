package cn.loveapp.logistics.common.service.impl;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.api.constant.BusinessType;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.OrderInfoDTO;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.AyLogisticsStatusConfig;
import cn.loveapp.logistics.common.constant.MongoConstant;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dao.mongo.LogisticsTraceInfoDao;
import cn.loveapp.logistics.common.dto.*;
import cn.loveapp.logistics.common.dto.request.AddMonitoringNumUseRecordRequest;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.response.AddMonitoringNumUseRecordResponse;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.*;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;
import cn.loveapp.logistics.common.service.external.TradePcService;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static java.util.Comparator.comparing;

/**
 * <AUTHOR>
 * @Date 2023/6/26 16:40
 */
@Service
public class LogisticsTraceHandleServiceImpl implements LogisticsTraceHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsTraceHandleServiceImpl.class);

    /**
     * 时间转换格式
     */
    private static final FastDateFormat fastDateFormat = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");

    /**
     * 排序规则-desc
     */
    private static final String SORT_DESC = "desc";

    /**
     * 排序规则-asc
     */
    private static final String SORT_ASC = "asc";

    @Autowired
    private LogisticsApiPlatformHandleService logisticsApiPlatformHandleService;


    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private LogisticsTraceInfoDao logisticsTraceInfoDao;


    @Autowired
    private LogisticsOrderHandleService logisticsOrderHandleService;

    @Autowired
    private TradePcService tradePcService;

    @Autowired
    private AyLogisticsStatusConfig ayLogisticsStatusConfig;

    /**
     * 查询并订阅接口消耗一个余额的sourceApp,重复调用也只扣除一次余额
     * 默认消耗两个余额,重复调用也会扣除多次余额
     */
    @Value("${logistics.service.searchAndSubscribe.deduction.quota.once.sourceApp:TAOTRADE,TRADEERP}")
    private List<PrepareConsignDTO.SourceAppEnum> searchAndSubscribeDeductionQuotaOnceSourceAppList;

    /**
     * erp额度扣除判断开关
     */
    @Value("${logistics.service.erp.deduction.enabled:false}")
    private Boolean erpDeductionEnabled;

    /**
     * 不扣额度的vipFlag列表
     */
    @Value("${logistics.service.vip.no.deduction.flags:6}")
    private List<Integer> noDeductionVipFlagList;

    @Override
    public boolean pullLogisticsTraceData(LogisticsHandleBo logisticsHandleBo) throws LogisticsHandlesException {

        // 推送物流轨迹入库
        if (Objects.isNull(logisticsHandleBo) || CollectionUtils.isEmpty(logisticsHandleBo.getMsgLogisticsTraceInfos())) {
            LOGGER.logError("物流入库异常：参数为空");
            return false;
        }
        String outSid = logisticsHandleBo.getOutSid();
        String appName = logisticsHandleBo.getAppName();
        String sellerId = logisticsHandleBo.getSellerId();
        String storeId = logisticsHandleBo.getStoreId();
        // 消息推送的物流轨迹（部分物流平台每次都推送全部的物流轨迹）
        List<LogisticsTraceInfo> msgLogisticsTraceInfos = logisticsHandleBo.getMsgLogisticsTraceInfos();


        // 此次新增物流轨迹（本次消息推送的新物流轨迹，与上次推送的时间对比）
        List<LogisticsTraceInfo> newLogisticsTraceInfos = null;
        // 待新入库的物流轨迹（排除已入库和无需入库的数据）
        List<LogisticsTraceInfo> saveLogisticsTraceInfos = null;

        try {
            if (CollectionUtils.isEmpty(msgLogisticsTraceInfos)) {
                LOGGER.logError("物流轨迹入库异常：消息为空");
                return false;
            }

            // 获取物流单
            LogisticsOrderInfo logisticsOrderInfo = ConvertUtil.getOrDefault(logisticsHandleBo.getLogisticsOrderInfo(),
                () -> logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName));
            if (logisticsOrderInfo == null) {
                LOGGER.logError("物流入库异常：参数为空");
                return false;
            }
            LogisticsTraceInfo logisticsTraceInfo = msgLogisticsTraceInfos.get(0);

            // 是否为物流单关联物流平台消息入库（当前库内物流平台和轨迹消息推过来的平台一直时才需要入库）
            boolean isAssociatedMsg = logisticsHandleBo.getAssociatedLogisticsStoreId().equals(logisticsTraceInfo.getLogisticsStoreId());

            if (!isAssociatedMsg) {
                // 快递鸟转下菜鸟
                if (Objects.equals(logisticsHandleBo.getAssociatedLogisticsStoreId(),
                    CommonLogisticsConstants.PLATFORM_KDNIAO)
                    && Objects.equals(logisticsTraceInfo.getLogisticsStoreId(),
                        CommonLogisticsConstants.PLATFORM_CAINIAO)) {
                    isAssociatedMsg = true;
                }
            }

            if (isAssociatedMsg) {
                // 物流单关联轨迹入库
                // 筛选新推送的消息，转发需要的轨迹列表（包含不入库的轨迹，例如TAO、PDD预发货订阅的非自有物流平台的轨迹推送）
                Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
                Date lastActionModified = logisticsOrderInfo.getLastActionModified();
                if (Objects.isNull(lastActionModified) || CollectionUtils.isEmpty(logisticsStatusList) || !logisticsHandleBo.isCheckModified()) {
                    // 新插入数据，上次更新action时间为空
                    newLogisticsTraceInfos = new ArrayList<>(msgLogisticsTraceInfos);
                } else {
                    // 比上次最后一次入库时间新的为新消息
                    newLogisticsTraceInfos = msgLogisticsTraceInfos.stream()
                        .filter(traceInfo -> (lastActionModified.before(traceInfo.getModified())
                            || !logisticsStatusList.contains(ayLogisticsStatusConfig
                                .getStatus(traceInfo.getAction(), traceInfo.getLogisticsStoreId()).value())))
                        .collect(Collectors.toList());
                }

                // 保存增量的新的轨迹数据
                if (CollectionUtils.isNotEmpty(newLogisticsTraceInfos)) {
                    logisticsTraceInfoDao.save(newLogisticsTraceInfos);
                }

                logisticsHandleBo.setNewLogisticsTraceInfos(newLogisticsTraceInfos);
                // 更新物流订阅记录中冗余的最新轨迹数据、转发router
                logisticsOrderHandleService.pullLogisticsData(logisticsHandleBo);

            } else {
                if (CollectionUtils.isNotEmpty(msgLogisticsTraceInfos)) {
                    LogisticsTraceInfo logisticsInfo = msgLogisticsTraceInfos.get(0);
                    String saveTid = logisticsInfo.getTid();
                    String savePlatformId = logisticsInfo.getPlatformId();
                    String saveAppName = logisticsInfo.getAppName();
                    String saveSellerId = logisticsInfo.getSellerId();
                    String saveLogisticsStoreId = logisticsInfo.getLogisticsStoreId();

                    if (CommonLogisticsConstants.PLATFORM_CAINIAO.equals(saveLogisticsStoreId)) {
                        List<LogisticsTraceInfo> logisticsTraceInfos =
                            logisticsTraceInfoDao.queryByTid(Lists.newArrayList(saveTid), saveSellerId, savePlatformId,
                                saveAppName, saveLogisticsStoreId);
                        if (CollectionUtils.isNotEmpty(logisticsTraceInfos)) {
                            LogisticsTraceInfo dbLastLogisticsTraceInfo = logisticsTraceInfos.stream()
                                .max(comparing(LogisticsTraceInfo::getModified)).orElse(null);
                            if (dbLastLogisticsTraceInfo != null) {
                                Date dbLastModified = dbLastLogisticsTraceInfo.getModified();
                                msgLogisticsTraceInfos = msgLogisticsTraceInfos.stream()
                                    .filter((traceInfo -> (dbLastModified.before(traceInfo.getModified()))))
                                    .collect(Collectors.toList());
                            }
                        }
                    }

                    if (CollectionUtils.isNotEmpty(msgLogisticsTraceInfos)) {
                        // 非物流单相关，直接保存
                        logisticsTraceInfoDao.save(msgLogisticsTraceInfos);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.logError("物流轨迹入库失败：" + e.getMessage(), e);
            throw new LogisticsHandlesException("物流轨迹入库失败：newLogisticsOrderInfo:" + JSON.toJSONString(saveLogisticsTraceInfos), e);
        }

        return true;
    }


    @Override
    public boolean subscribeLogisticsTrace(LogisticsOrderSubscribeDTO logisticsHandle) throws LogisticsHandlesException {
        String logisticsAppName = logisticsHandle.getLogisticsAppName();
        String logisticsStoreId = logisticsHandle.getLogisticsStoreId();
        if (StringUtils.isAnyEmpty(logisticsAppName, logisticsStoreId)) {
            throw new LogisticsHandlesException("未指定快递平台");
        }

        // 检查下物流公司是否映射正确
        if (!checkLogisticsCompanyMapping(logisticsHandle, logisticsStoreId, logisticsAppName)) {
            return false;
        }

        AySubscribeLogisticsTraceRequest request = new AySubscribeLogisticsTraceRequest();
        request.setLogisticsHandle(logisticsHandle);
        request.setNeedDeductionQuota(logisticsHandle.isSubscribeNeedDeductionQuota());
        boolean isSubscribeSuccess = false;
        String errorMsg = null;
        // 白名单才订阅
        AySubscribeLogisticsTraceResponse aySubscribeLogisticsTraceResponse = logisticsApiPlatformHandleService.subscribeLogisticsTrace(request, logisticsStoreId, logisticsAppName);
        if (aySubscribeLogisticsTraceResponse == null || !aySubscribeLogisticsTraceResponse.isSuccess()) {
            LOGGER.logError("物流订阅失败：" + JSON.toJSONString(aySubscribeLogisticsTraceResponse));
            return false;
        } else {
            isSubscribeSuccess = true;
        }
        // 物流订阅记录入库
        LogisticsHandleBo logisticsHandleBo = LogisticsHandleBo.generalLogisticsHandleBo(logisticsHandle, isSubscribeSuccess, errorMsg);
        logisticsOrderHandleService.pullLogisticsData(logisticsHandleBo);
        return isSubscribeSuccess;
    }

    @Override
    public boolean subscribeLogisticsTrace(LogisticsOrderSubscribeDTO logisticsHandle, List<OrderInfoDTO> orderInfoDTOList)
        throws LogisticsHandlesException {
        String logisticsAppName = logisticsHandle.getLogisticsAppName();
        String logisticsStoreId = logisticsHandle.getLogisticsStoreId();
        if (StringUtils.isAnyEmpty(logisticsAppName, logisticsStoreId)) {
            throw new LogisticsHandlesException("未指定快递平台");
        }
        boolean isSubscribeSuccess = false;
        String errorMsg = null;
        // 检查下物流公司是否映射正确
        if (checkLogisticsCompanyMapping(logisticsHandle, logisticsStoreId, logisticsAppName)) {
            AySubscribeLogisticsTraceRequest request = new AySubscribeLogisticsTraceRequest();
            request.setLogisticsHandle(logisticsHandle);
            request.setNeedDeductionQuota(logisticsHandle.isSubscribeNeedDeductionQuota());

            // 白名单才订阅
            AySubscribeLogisticsTraceResponse aySubscribeLogisticsTraceResponse =
                    logisticsApiPlatformHandleService.subscribeLogisticsTrace(request, logisticsStoreId, logisticsAppName);
            if (aySubscribeLogisticsTraceResponse == null) {
                errorMsg = "接口调用异常";
            } else if (!aySubscribeLogisticsTraceResponse.isSuccess()) {
                LOGGER.logError("物流订阅失败：" + JSON.toJSONString(aySubscribeLogisticsTraceResponse));
                errorMsg = aySubscribeLogisticsTraceResponse.getErrorMsg();
            } else {
                isSubscribeSuccess = true;
            }
        }

        // 物流订阅记录入库
        LogisticsHandleBo logisticsHandleBo =
            LogisticsHandleBo.generalLogisticsHandleBo(logisticsHandle, isSubscribeSuccess, errorMsg, orderInfoDTOList);
        logisticsOrderHandleService.pullLogisticsData(logisticsHandleBo);
        return isSubscribeSuccess;
    }



    /**
     * 检验物流映射
     * @param logisticsHandle
     * @param logisticsStoreId
     * @param logisticsAppName
     */
    private boolean checkLogisticsCompanyMapping(LogisticsOrderSubscribeDTO logisticsHandle, String logisticsStoreId,
                                                 String logisticsAppName) {
        if (StringUtils.isEmpty(logisticsHandle.getLogisticsCompanyCode())) {
            if (StringUtils.isEmpty(logisticsHandle.getSourceLogisticsCompany())) {
                LOGGER.logError("快递公司不能为空");
                return false;
            }

            LogisticsCompanyInfoDTO sourceCompanyInfo = new LogisticsCompanyInfoDTO();
            sourceCompanyInfo.setLogisticsStoreId(logisticsHandle.getStoreId());
            sourceCompanyInfo.setCompanyCode(logisticsHandle.getSourceLogisticsCompany());
            sourceCompanyInfo.setCompanyName(logisticsHandle.getSourceLogisticsCompany());
            sourceCompanyInfo.setCompanyId(logisticsHandle.getSourceLogisticsCompany());
            sourceCompanyInfo.setOutSid(logisticsHandle.getOutSid());
            sourceCompanyInfo.setLogisticsAppName(logisticsHandle.getLogisticsAppName());
            LogisticsCompanyInfoDTO targetCompanyInfo = null;
            try {
                targetCompanyInfo =
                    logisticsOrderHandleService.logisticsCompanyTransform(sourceCompanyInfo, logisticsStoreId);
            } catch (LogisticsHandlesException e) {
                throw new RuntimeException(e);
            }

            if (Objects.isNull(targetCompanyInfo) || StringUtils.isEmpty(targetCompanyInfo.getCompanyCode())) {
                LOGGER.logError("快递公司获取异常");
                return false;
            }

            logisticsHandle.setLogisticsCompanyCode(targetCompanyInfo.getCompanyCode());
            logisticsHandle.setLogisticsCompanyName(targetCompanyInfo.getCompanyName());
        } else if (StringUtils.isNotEmpty(logisticsHandle.getOriginalLogisticsStoreId())
            && CommonLogisticsConstants.PLATFORM_KDNIAO.equals(logisticsHandle.getOriginalLogisticsStoreId())) {
            // todo: 前段传的还是快递鸟 并且直接传的code 需要补一遍映射(前端统一逻辑后删除)
            LogisticsCompanyInfoDTO sourceCompanyInfo = new LogisticsCompanyInfoDTO();
            sourceCompanyInfo.setLogisticsStoreId(logisticsHandle.getLogisticsStoreId());
            sourceCompanyInfo.setCompanyCode(logisticsHandle.getLogisticsCompanyCode());
            sourceCompanyInfo.setCompanyName(logisticsHandle.getLogisticsCompanyName());
            sourceCompanyInfo.setOutSid(logisticsHandle.getOutSid());
            sourceCompanyInfo.setLogisticsStoreId(logisticsHandle.getOriginalLogisticsStoreId());
            LogisticsCompanyInfoDTO targetCompanyInfo = null;
            try {
                targetCompanyInfo =
                    logisticsOrderHandleService.logisticsCompanyTransform(sourceCompanyInfo, logisticsStoreId);
            } catch (LogisticsHandlesException e) {
                throw new RuntimeException(e);
            }

            if (Objects.isNull(targetCompanyInfo) || StringUtils.isEmpty(targetCompanyInfo.getCompanyCode())) {
                LOGGER.logError("快递公司获取异常");
                return false;
            }

            logisticsHandle.setLogisticsCompanyCode(targetCompanyInfo.getCompanyCode());
            logisticsHandle.setLogisticsCompanyName(targetCompanyInfo.getCompanyName());
        }

        return true;
    }


    @Override
    public List<LogisticsDetailDTO> searchLogisticsTraceFromApi(List<LogisticsInfoApiSearchDTO> apiSearchList, UserInfoDTO userInfoDTO, PrepareConsignDTO prepareConsignDTO) throws LogisticsHandlesException {
        PrepareConsignDTO.SourceAppEnum sourceApp = prepareConsignDTO.getSourceApp();
        boolean forceSubscribe = prepareConsignDTO.isForceSubscribe();
        boolean reportedUsedWhenSubscribe = prepareConsignDTO.isReportedUsedWhenSubscribe();

        // 调用该接口表示库里未查到，如果api（非电商平台）能查到，则表示漏订阅了，查到后补充订阅逻辑
        List<LogisticsOrderSubscribeDTO> subscribeHandle = new ArrayList<>();
        List<LogisticsOrderSubscribeDTO> forceSubscribeHandle = new ArrayList<>();

        // 需要直接入库的列表
        List<SaveDbLogisticsInfoDTO> saveHandles = Lists.newArrayList();
        List<LogisticsDetailDTO> logisticsDetails = new ArrayList<>();
        for (LogisticsInfoApiSearchDTO logisticsInfoApiSearch : apiSearchList) {
            String logisticsStoreId = LogisticsUtil.getDefaultLogisticsStoreId(logisticsInfoApiSearch.getLogisticsStoreId());
            logisticsInfoApiSearch.setLogisticsStoreId(logisticsStoreId);
            String logisticsAppName = logisticsInfoApiSearch.getLogisticsAppName();
            String outSid = logisticsInfoApiSearch.getOutSid();
            String sellerId = logisticsInfoApiSearch.getSellerId();
            String storeId = logisticsInfoApiSearch.getStoreId();
            String appName = logisticsInfoApiSearch.getAppName();
            String sellerNick = logisticsInfoApiSearch.getSellerNick();


            LogisticsOrderSubscribeDTO logisticsHandle = new LogisticsOrderSubscribeDTO();
            BeanUtils.copyProperties(logisticsInfoApiSearch, logisticsHandle);
            //解决强制订阅时sourceApp遗漏的问题
            logisticsHandle.setSourceApp(sourceApp.name());
            // 待强制订阅集合
            if (forceSubscribe) {
                forceSubscribeHandle.add(logisticsHandle);
            }

            LogisticsDetailDTO logisticsDetail = new LogisticsDetailDTO();
            AySearchLogisticsTraceRequest request = new AySearchLogisticsTraceRequest();
            request.setOutSid(outSid);
            request.setSellerId(sellerId);
            request.setStoreId(storeId);
            request.setAppName(appName);
            request.setSellerNick(sellerNick);
            request.setSourceApp(sourceApp);
            request.setLogisticsCompanyCode(logisticsInfoApiSearch.getLogisticsCompanyCode());
            request.setCustomerName(logisticsInfoApiSearch.getCustomerName());
            request.setPhone(logisticsInfoApiSearch.getPhone());
            logisticsDetail.setOutSid(outSid);

            // 如果未指定api物流公司，根据源订单物流公司（电商平台）转换映射
            if (StringUtils.isEmpty(logisticsInfoApiSearch.getLogisticsCompanyCode())) {
                if (StringUtils.isEmpty(logisticsInfoApiSearch.getSourceLogisticsCompany())) {
                    throw new LogisticsHandlesException("快递公司不能为空: 运单号" + outSid);
                }
                LogisticsCompanyInfoDTO sourceCompanyInfo = new LogisticsCompanyInfoDTO();
                sourceCompanyInfo.setLogisticsStoreId(storeId);
                sourceCompanyInfo.setCompanyCode(logisticsInfoApiSearch.getSourceLogisticsCompany());
                sourceCompanyInfo.setCompanyName(logisticsInfoApiSearch.getSourceLogisticsCompany());
                sourceCompanyInfo.setCompanyId(logisticsInfoApiSearch.getSourceLogisticsCompany());
                sourceCompanyInfo.setOutSid(logisticsInfoApiSearch.getOutSid());
                sourceCompanyInfo.setLogisticsAppName(logisticsInfoApiSearch.getLogisticsAppName());
                LogisticsCompanyInfoDTO targetCompanyInfo = logisticsOrderHandleService.logisticsCompanyTransform(sourceCompanyInfo, logisticsStoreId);
                if (Objects.isNull(targetCompanyInfo) || StringUtils.isEmpty(targetCompanyInfo.getCompanyCode())) {
                    throw new LogisticsHandlesException("快递公司获取异常，无法订阅: 运单号" + outSid);
                }
                request.setLogisticsCompanyCode(targetCompanyInfo.getCompanyCode());
                logisticsDetail.setLogisticsStoreId(targetCompanyInfo.getLogisticsStoreId());
                logisticsDetail.setCompanyCode(targetCompanyInfo.getCompanyCode());
                logisticsDetail.setCompanyName(targetCompanyInfo.getCompanyName());
                logisticsInfoApiSearch.setLogisticsCompanyCode(targetCompanyInfo.getCompanyCode());
            }


            if (Objects.equals(PrepareConsignDTO.SourceAppEnum.TRADEERP, sourceApp)) {
                // erp全部免费
                logisticsHandle.setSubscribeNeedDeductionQuota(false);
                logisticsHandle.setSearchNeedDeductionQuota(false);

                if (BooleanUtils.isTrue(erpDeductionEnabled) && !noDeductionVipFlagList.contains(userInfoDTO.getVipFlag())) {
                    BusinessType businessType = logisticsInfoApiSearch.getBusinessType();
                    if (BusinessType.order.equals(businessType)) {
                        // 普通订单查询不扣额度，只有手工单扣
                        request.setNeedDeductionQuota(false);
                    } else {
                        request.setNeedDeductionQuota(true);
                    }
                } else {
                    request.setNeedDeductionQuota(false);
                }
            } else {
                // 订阅过物流的不需要扣费
                if (searchAndSubscribeDeductionQuotaOnceSourceAppList.contains(sourceApp)) {
                    //当前sourceApp查询后订阅无需扣费
                    logisticsHandle.setSubscribeNeedDeductionQuota(false);

                    LogisticsOrderInfo lastLogisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSaveLogisticsId(outSid, logisticsStoreId, Arrays.asList(MongoConstant.TID_FIELD), sellerId, storeId, appName);
                    if (lastLogisticsOrderInfo != null) {
                        LOGGER.logInfo("已存在订阅记录，本次调用无需扣除物流包余额");
                        request.setNeedDeductionQuota(false);
                        //设置查询未扣费
                        logisticsHandle.setSearchNeedDeductionQuota(false);
                        //TODO 已经存在订阅记录，可以优化为不需要补充订阅，但是订阅记录内存在需要轨迹字段可能未更新，这里暂未从订阅集合中去除
                    }
                }
            }

            // 调用api查询
            AySearchLogisticsTraceResponse searchResponse = logisticsApiPlatformHandleService
                .searchLogisticsTrace(request, userInfoDTO, logisticsStoreId, logisticsAppName);
            List<LogisticsInfoDTO> logisticsInfos = searchResponse.getLogisticsInfos();
            if (CollectionUtils.isNotEmpty(logisticsInfos)) {
                TracesDTO traceList = new TracesDTO();
                List<TransitStepDTO> transitStepInfo = new ArrayList<>();
                for (LogisticsInfoDTO logisticsInfo : logisticsInfos) {
                    TransitStepDTO transitStep = new TransitStepDTO();
                    transitStep.setAction(logisticsInfo.getAction());
                    if (LogisticsUtil.isTradeERP(appName)) {
                        transitStep.setStatus(
                            ayLogisticsStatusConfig.getStatus(logisticsInfo.getAction(), logisticsStoreId).value());
                    } else {
                        transitStep.setStatus(logisticsInfo.getStatus());
                    }
                    transitStep.setStatusTime(fastDateFormat.format(logisticsInfo.getModified()));
                    transitStep.setStatusDesc(logisticsInfo.getDesc());
                    transitStepInfo.add(transitStep);

                    // 补充物流用户信息
                    logisticsInfo.setSellerId(sellerId);
                    logisticsInfo.setAppName(appName);
                    logisticsInfo.setPlatformId(storeId);
                }
                String sort = logisticsInfoApiSearch.getSort();
                if (SORT_DESC.equals(sort)) {
                    traceList.setTransitStepInfo(transitStepInfo.stream()
                        .sorted(comparing(TransitStepDTO::getStatusTime)
                            .thenComparing(TransitStepDTO::getAction).reversed())
                        .collect(Collectors.toList()));

                } else if (SORT_ASC.equals(sort)) {
                    traceList.setTransitStepInfo(transitStepInfo.stream().sorted(comparing(TransitStepDTO::getStatusTime).thenComparing(TransitStepDTO::getAction))
                        .collect(Collectors.toList()));
                }
                logisticsDetail.setTraceList(traceList);
                logisticsDetails.add(logisticsDetail);

                // 能查到则补个订阅
                subscribeHandle.add(logisticsHandle);

                // 查到直接入库
                SaveDbLogisticsInfoDTO saveDbLogisticsInfoDTO = new SaveDbLogisticsInfoDTO();
                saveDbLogisticsInfoDTO.setLogisticsInfoDTOS(logisticsInfos);
                saveDbLogisticsInfoDTO.setLogisticsStoreId(logisticsStoreId);
                saveDbLogisticsInfoDTO.setLogisticsAppName(logisticsAppName);
                saveHandles.add(saveDbLogisticsInfoDTO);
            }
        }

        //强制订阅 或者 存在物流轨迹时 需要补充订阅,合并并去重强制订阅和有物流轨迹订阅
        subscribeHandle.addAll(forceSubscribeHandle);
        HashSet<LogisticsOrderSubscribeDTO> finalSubscribeList = new HashSet<>(subscribeHandle);
        if (CollectionUtils.isNotEmpty(finalSubscribeList)) {
            LOGGER.logInfo("补充订阅条数：" + finalSubscribeList.size());
            finalSubscribeList.forEach(subscribe -> {
                try {
                    subscribeLogisticsTrace(subscribe);
                } catch (LogisticsHandlesException e) {
                    LOGGER.logError("补订阅失败：" + e.getMessage(), e);
                }
            });

            //上报消耗记录数据,只有查询扣费（扣除余额）的数据才需要上报
            if (reportedUsedWhenSubscribe) {
                List<AddMonitoringNumUseRecordRequest.MonitoringNumUseRecord> reportedUseRecordList = finalSubscribeList.stream()
                    .filter(m->m.isSearchNeedDeductionQuota()).map(m -> {
                    AddMonitoringNumUseRecordRequest.MonitoringNumUseRecord useRecord = new AddMonitoringNumUseRecordRequest.MonitoringNumUseRecord();
                    useRecord.setUseQuantity(1);
                    useRecord.setUseType(m.getBusinessType());
                    useRecord.setWaybillNo(m.getOutSid());

                    useRecord.setSellerNick(userInfoDTO.getNick());
                    useRecord.setSellerId(userInfoDTO.getSellerId());
                    useRecord.setStoreId(userInfoDTO.getStoreId());
                    useRecord.setAppName(userInfoDTO.getAppName());

                    useRecord.setSubscribeSellerNick(m.getSellerNick());
                    useRecord.setSubscribeSellerId(m.getSellerId());
                    useRecord.setSubscribeStoreId(m.getStoreId());
                    useRecord.setSubscribeAppName(m.getAppName());
                    return useRecord;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reportedUseRecordList)) {
                    AddMonitoringNumUseRecordRequest request = new AddMonitoringNumUseRecordRequest(reportedUseRecordList);
                    LOGGER.logInfo("调用上报消耗记录接口：request：" + JSON.toJSONString(request));
                    CommonApiResponse<AddMonitoringNumUseRecordResponse> response = tradePcService.addMonitoringNumUesRecord(request);
                    LOGGER.logInfo("调用上报消耗记录接口：response：" + JSON.toJSONString(response));
                }
            }
        }

        // 入库消息（在订阅之后，运单主表存在记录时入库）
        if (CollectionUtils.isNotEmpty(saveHandles)) {
            saveHandles.forEach(save -> {
                // 发送入库消息(查询到直接入库，减少api调用)
                logisticsSendHandleService.pushLogisticsSaveMsg(save.getLogisticsInfoDTOS(), true,
                    save.getLogisticsStoreId(), save.getLogisticsAppName());
            });
        }

        return logisticsDetails;
    }

    @Override
    public List<LogisticsTraceInfo> queryLogisticsTraceList(LogisticsOrderInfo logisticsOrderInfo) {
        if (Objects.isNull(logisticsOrderInfo)) {
            return null;
        }
        LogisticsOrderInfo.BusinessInfo businessInfo = logisticsOrderInfo.getBusinessInfo();
        BusinessType businessType = logisticsOrderInfo.getBusinessType();

        String sellerId = logisticsOrderInfo.getSellerId();
        String appName = logisticsOrderInfo.getAppName();
        String storeId = logisticsOrderInfo.getStoreId();
        String outSid = logisticsOrderInfo.getOutSid();

        List<LogisticsTraceInfo> logisticsTraceInfos = null;
        boolean isTaoBaoPlatformLogistics =
            CommonPlatformConstants.PLATFORM_TAO.equals(storeId) && !Objects.isNull(businessInfo)
                && CollectionUtils.isNotEmpty(businessInfo.getTidList()) && BusinessType.order.equals(businessType)
                && Objects.equals(CommonPlatformConstants.PLATFORM_TAO, logisticsOrderInfo.getSaveLogisticsStoreId());
        if (isTaoBaoPlatformLogistics) {
            // 淘宝正向物流存的是tid(自由打印走的第三方平台物流) 预发货的订单物流则需要走菜鸟流程
            logisticsTraceInfos = logisticsTraceInfoDao.queryByTid(Lists.newArrayList(businessInfo.getTidList()),
                sellerId, storeId, appName, logisticsOrderInfo.getSaveLogisticsStoreId());
        } else {
            if (CommonLogisticsConstants.PLATFORM_CAINIAO.equals(logisticsOrderInfo.getSaveLogisticsStoreId())) {
                // 库里保证只有一条记录，当菜鸟物流入库时，库里的老数据可能是快递鸟需要兼容下
                logisticsTraceInfos = logisticsTraceInfoDao.queryByTidAndStoreIds(Lists.newArrayList(outSid), sellerId,
                    storeId, appName, Lists.newArrayList(logisticsOrderInfo.getSaveLogisticsStoreId(),
                        CommonLogisticsConstants.PLATFORM_KDNIAO));
            } else {
                logisticsTraceInfos = logisticsTraceInfoDao.queryByTid(Lists.newArrayList(outSid), sellerId, storeId,
                    appName, logisticsOrderInfo.getSaveLogisticsStoreId());
            }
        }

        return logisticsTraceInfos;
    }

    /**
     * 生成需求更新的物流轨迹列表
     *
     * @param lastLogisticsTraceInfos 已存在
     * @param logisticsTraceInfos     新消息
     * @return
     */
    private List<LogisticsTraceInfo> generalLogisticsTraceInfos(List<LogisticsTraceInfo> lastLogisticsTraceInfos, List<LogisticsTraceInfo> logisticsTraceInfos) {
        // 与已入库的物流轨迹对比，去重
        List<LogisticsTraceInfo> newLogisticsTraceInfos = new ArrayList<>();
        Set<String> existingActions = lastLogisticsTraceInfos.stream()
            .map(LogisticsTraceInfo::getActionAndDescAndModified)
            .collect(Collectors.toSet());

        logisticsTraceInfos.forEach(logisticsTraceInfo -> {
            if (!existingActions.contains(logisticsTraceInfo.getActionAndDescAndModified())) {
                newLogisticsTraceInfos.add(logisticsTraceInfo);
            }
        });
        return newLogisticsTraceInfos;
    }
}

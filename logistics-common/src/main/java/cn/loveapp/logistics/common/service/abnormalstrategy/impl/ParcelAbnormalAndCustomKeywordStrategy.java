package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import static org.elasticsearch.index.query.QueryBuilders.*;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Component;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;

/**
 * <AUTHOR>
 * @date 2025-03-19 18:37
 * @description: 异常包裹（丢失、拒收、退回）和自定义关键字异常（erp新增）
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class ParcelAbnormalAndCustomKeywordStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(FirstTraceAfterPickedUpTimeoutStrategy.class);

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.ABNORMAL_PARCEL_AND_CUSTOM_KEYWORD.value();
    }

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (!LogisticsUtil.isTradeERP(appName)) {
            // 非erp跳过
            return new ExecuteResult();
        }

        boolean abnormalParcelCustomKeywordEnable = getSetting(userSettings,
            UserAbnormalSettingConstant.ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_ENABLE, true, Boolean.class);
        if (!abnormalParcelCustomKeywordEnable) {
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime lastActionModified = DateUtil.parseDate(logisticsOrderInfo.getLastActionModified());

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        boolean isHitKeyword = false;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            isHitKeyword = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.EXCLUDE_OTHER_ABNORMAL_LIST::contains);
        }

        String keywordStr = null;

        if (!isHitKeyword) {
            String setting = getSetting(userSettings,
                UserAbnormalSettingConstant.ABNORMAL_LOGISTICS_ABNORMAL_PARCEL_CUSTOM_KEYWORD, null, String.class);
            if (StringUtils.isEmpty(setting)) {
                return new ExecuteResult();
            }

            String[] split = setting.split(",");
            String lastTraceDesc = logisticsOrderInfo.getLastTraceDesc();
            if (StringUtils.isNotEmpty(lastTraceDesc)) {
                for (String keyword : split) {
                    if (lastTraceDesc.contains(keyword)) {
                        isHitKeyword = true;
                        keywordStr = keyword;
                        break;
                    }
                }
            }

        }

        LogisticsOrderInfo.AbnormalDetails parcelAbnormalAndCustomKeyword =
            logisticsAbnormalInfo.getParcelAbnormalAndCustomKeyword();
        if (Objects.isNull(parcelAbnormalAndCustomKeyword)) {
            parcelAbnormalAndCustomKeyword = new LogisticsOrderInfo.AbnormalDetails();
        }

        boolean isUpdate = LogisticsOrderInfo.generalAbnormalDetails(isHitKeyword, parcelAbnormalAndCustomKeyword, null);

        if (isHitKeyword || isUpdate) {
            String description = StringUtils.isEmpty(keywordStr) ? "执行策略：【异常包裹直接命中无需校验关键字】, 判断结果："
                : "执行策略：【异常包裹关键字异常 " + keywordStr + "】，判断结果：";
            LOGGER.logInfo(logisticsOrderInfo.getOutSid() + description + isHitKeyword + ", 是否变更：" + isUpdate);
        }

        if (isUpdate) {
            logisticsAbnormalInfo.setParcelAbnormalAndCustomKeyword(parcelAbnormalAndCustomKeyword);
        }

        if (isUpdate && isHitKeyword) {
            appendLogisticsAbnormalTypeHistory(logisticsOrderInfo, getAbnormalType());
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(isHitKeyword, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModified));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getParcelAbnormalAndCustomKeywordCount();
        if (countAll == null) {
            countAll = 0;
        }

        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setParcelAbnormalAndCustomKeywordCount(countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.parcelAbnormalAndCustomKeywordIsExists, true))
            .must(existsQuery(EsFields.consignTime));
        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }

        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(EsFields.parcelAbnormalAndCustomKeywordProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(EsFields.parcelAbnormalAndCustomKeywordProcessStatus))));
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails parcelAbnormalAndCustomKeyword =
            logisticsAbnormalInfo.getParcelAbnormalAndCustomKeyword();
        if (Objects.isNull(parcelAbnormalAndCustomKeyword)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(parcelAbnormalAndCustomKeyword.getProcessStatus(),
            newProcessStatus, appName)) {
            parcelAbnormalAndCustomKeyword.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setParcelAbnormalAndCustomKeyword(parcelAbnormalAndCustomKeyword);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal())
            || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getParcelAbnormalAndCustomKeyword();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists())
            && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }

    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime checkTime) {
        int checkTimeMin = checkConfig.getParcelAbnormalAndCustomKeywordMinCheckTime();
        int checkTimeMax = checkConfig.getParcelAbnormalAndCustomKeywordMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, checkTime);

    }
}

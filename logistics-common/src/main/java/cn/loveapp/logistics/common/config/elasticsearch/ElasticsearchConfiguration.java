package cn.loveapp.logistics.common.config.elasticsearch;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * ESConfig
 *
 * <AUTHOR>
 * @date 2020/2/18
 */
@Data
@Configuration
public class ElasticsearchConfiguration {
    /**
     * 索引数量
     */
    @Value("${logistics.elasticsearch.index.num: 5}")
    private int indexNum;

    /**
     * 是否启用ES Filter
     */
    @Value("${logistics.elasticsearch.filter.enable: true}")
    private boolean filterEnable = true;

    /**
     * scroll窗口 超时时间
     */
    @Value("${logistics.elasticsearch.scrollTimeInMillis: 5000}")
    private long scrollTimeInMillis;

}

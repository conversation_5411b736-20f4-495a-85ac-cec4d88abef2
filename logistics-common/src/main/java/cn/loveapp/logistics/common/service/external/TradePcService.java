package cn.loveapp.logistics.common.service.external;

import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.logistics.common.dto.request.AddMonitoringNumUseRecordRequest;
import cn.loveapp.logistics.common.dto.request.MonitoringNumWithholdingRequset;
import cn.loveapp.logistics.common.dto.request.ShopsNoticeRequest;
import cn.loveapp.logistics.common.dto.request.LogisticsCompanyTransformRequest;
import cn.loveapp.logistics.common.dto.response.AddMonitoringNumUseRecordResponse;
import cn.loveapp.logistics.common.dto.response.MonitoringNumWithholdingResponse;
import cn.loveapp.logistics.common.dto.response.ShopsNoticeResponse;
import cn.loveapp.logistics.common.dto.response.LogisticsCompanyTransformResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.RequestMapping;


/**
 * TradePcService
 *
 * <AUTHOR>
 * @date 2022/10/5
 */
@FeignClient(name = "tradepcService")
public interface TradePcService {

    /**
     * 物流公司转换查询接口
     *
     * @param request
     * @return
     */
    @RequestMapping("/Logistics/transformCompanyInfo")
    CommonApiResponse<LogisticsCompanyTransformResponse> logisticsTransformCompanyInfo(LogisticsCompanyTransformRequest request);


    /**
     * 多店消息弹窗提醒
     * @param request
     * @return
     */
    @RequestMapping("/multiterminal/handelPushNoticeShopMessage")
    CommonApiResponse<ShopsNoticeResponse> handelPushNoticeShopMessage(ShopsNoticeRequest request);



    /**
     * 查看物流监控余额并预扣除
     * @param request
     * @return
     */
    @RequestMapping("/trade/getMonitoringNumWithholding")
    CommonApiResponse<MonitoringNumWithholdingResponse> getMonitoringNumWithholding(MonitoringNumWithholdingRequset request);

    /**
     * 添加物流包余额消耗记录
     * @param request
     * @return
     */
    @RequestMapping("/iytrade2/setLogisticsQuotaConsumptionRecord")
    CommonApiResponse<AddMonitoringNumUseRecordResponse> addMonitoringNumUesRecord(AddMonitoringNumUseRecordRequest request);
}

package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.constant.*;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static org.elasticsearch.index.query.QueryBuilders.*;
import static org.elasticsearch.index.query.QueryBuilders.existsQuery;

/**
 * 物流异常策略 揽收后签收前轨迹超N小时未更新
 * <p>
 * LogisticsAbnormalType.TRACKING_NOT_UPDATE_AFTER_OF_PICKUP
 *
 * <AUTHOR>
 * @Date 2023/6/21 12:09
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class TrackingNotUpdatedAfterOfPickupStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TrackingNotUpdatedAfterOfPickupStrategy.class);

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;
        // 设置可配置 揽收后签收前轨迹超N小时未更新 默认24
        int checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_UPDATE_TIMEOUT, 24, Integer.class);
        Date lastActionModified = logisticsOrderInfo.getLastActionModified();
        if (Objects.isNull(lastActionModified)) {
            return new ExecuteResult(false, getAbnormalDeadline(null));
        }
        // 上次轨迹变更时间
        LocalDateTime lastActionModifiedTime = DateUtil.parseDate(lastActionModified);
        // N小时未更新
        LocalDateTime deadline = lastActionModifiedTime.plusHours(checkTime);
        boolean updateAfterCheckTime = deadline.isBefore(now);

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();
        // 已揽收
        boolean collected = false;
        // 已签收
        boolean delivered = false;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            collected = logisticsStatusList.contains(AyLogisticsStatus.PICKED_UP.value());
            delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
        }

        // 揽收后签收前轨迹超N小时未更新 最后一次更新轨迹时间 + N小时 < 当前时间  && 物流轨迹已揽收 && 物流轨迹未签收
        boolean checkTrackingNotUpdatedAfterOfPickup = updateAfterCheckTime && collected && !delivered;

        LogisticsOrderInfo.AbnormalDetails trackingNotUpdatedAfterOfPickup = logisticsAbnormalInfo.getTrackingNotUpdatedAfterOfPickup();
        if (Objects.isNull(trackingNotUpdatedAfterOfPickup)) {
            trackingNotUpdatedAfterOfPickup = new LogisticsOrderInfo.AbnormalDetails();
        }

        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkTrackingNotUpdatedAfterOfPickup, trackingNotUpdatedAfterOfPickup, null);

        if (checkTrackingNotUpdatedAfterOfPickup || isUpdate) {
            LOGGER.logInfo("执行策略：【揽收后签收前轨迹超" + checkTime + "未更新】，判断结果：" + checkTrackingNotUpdatedAfterOfPickup
                + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setTrackingNotUpdatedAfterOfPickup(trackingNotUpdatedAfterOfPickup);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkTrackingNotUpdatedAfterOfPickup, isUpdate);
        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModifiedTime));
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getTrackingNotUpdatedAfterOfPickupCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setTrackingNotUpdatedAfterOfPickupCount(countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.trackingNotUpdatedAfterOfPickupIsExists, true))
            .mustNot(termQuery(EsFields.trackingNotUpdatedAfterOfPickupProcessStatus, AbnormalProcessStatus.PROCESSED));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(EsFields.trackingNotUpdatedAfterOfPickupProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(EsFields.trackingNotUpdatedAfterOfPickupProcessStatus))));
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails trackingNotUpdatedAfterOfPickup =
            logisticsAbnormalInfo.getTrackingNotUpdatedAfterOfPickup();
        if (Objects.isNull(trackingNotUpdatedAfterOfPickup)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(trackingNotUpdatedAfterOfPickup.getProcessStatus(),
            newProcessStatus, appName)) {
            trackingNotUpdatedAfterOfPickup.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setTrackingNotUpdatedAfterOfPickup(trackingNotUpdatedAfterOfPickup);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getTrackingNotUpdatedAfterOfPickup();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.TRACKING_NOT_UPDATE_AFTER_OF_PICKUP.value();
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime lastActionModifiedTime) {
        int checkTimeMin = checkConfig.getTrackingNotUpdatedAfterOfPickupMinCheckTime();
        int checkTimeMax = checkConfig.getTrackingNotUpdatedAfterOfPickupMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, lastActionModifiedTime);
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_UN_UPDATE_TIMEOUT_PROMPT, false, Boolean.class);
    }
}

package cn.loveapp.logistics.common.config.mongodb;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDbFactory;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MappedDocument;
import org.springframework.data.mongodb.core.MongoAction;
import org.springframework.data.mongodb.core.MongoActionOperation;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultDbRefResolver;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.lang.Nullable;
import org.springframework.util.StringUtils;

import com.mongodb.*;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.model.InsertManyOptions;
import com.mongodb.connection.ClusterConnectionMode;
import com.mongodb.connection.ConnectionPoolSettings.Builder;
import com.mongodb.connection.ServerSettings;
import com.mongodb.connection.SocketSettings;

/**
 * LogisticsMongoDbConfiguration
 *
 * <AUTHOR>
 * @date 2022/1/24
 */
@Configuration
@EnableConfigurationProperties(MongoDBProperties.class)
@EnableMongoRepositories
public class LogisticsMongoDbConfiguration extends AbstractMongoClientConfiguration {

    @Autowired
    public MongoDbFactory mongoDbFactory;

    @Autowired
    public MongoMappingContext mongoMappingContext;

    public MongoDBProperties mongoDBProperties;

    public LogisticsMongoDbConfiguration(MongoDBProperties mongoDBProperties) {
        this.mongoDBProperties = mongoDBProperties;
    }

    /**
     * Return the {@link MongoClient} instance to connect to. Annotate with {@link Bean} in case you want to expose a
     * {@link MongoClient} instance to the {@link ApplicationContext}.
     */
    @Override
    public MongoClient mongoClient() {
        String srvHost = mongoDBProperties.getSrvHost();
        List<String> hosts = mongoDBProperties.getHosts();
        List<ServerAddress> addressList = new ArrayList<>();
        for (int i = 0; i < hosts.size(); i++) {
            ServerAddress address = new ServerAddress(hosts.get(i), mongoDBProperties.getPort());
            addressList.add(address);
        }
        MongoClientSettings mongoClientSettings = MongoClientSettings
            .builder().credential(MongoCredential.createCredential(mongoDBProperties.getAccount(),
                mongoDBProperties.getDbName(), mongoDBProperties.getPassword().toCharArray()))
            .applyToClusterSettings(builder -> {
                if (!StringUtils.isEmpty(srvHost)) {
                    builder.srvHost(srvHost);
                } else {
                    builder.hosts(addressList);
                }
                builder.mode(ClusterConnectionMode.MULTIPLE).serverSelectionTimeout(25000, TimeUnit.MILLISECONDS)
                    .localThreshold(30, TimeUnit.MILLISECONDS);
            }).applyToSslSettings(builder -> builder.enabled(false)).readPreference(ReadPreference.primaryPreferred())
            .writeConcern(mongoDBProperties.getWriteConcern()).readConcern(ReadConcern.LOCAL)
            .retryWrites(mongoDBProperties.getRetryWrites())
            .compressorList(Collections.singletonList(MongoCompressor.createSnappyCompressor()))
            .applyToConnectionPoolSettings(new Block<Builder>() {
                /**
                 * Apply some logic to the value.
                 *
                 * @param builder
                 *            the value to apply to
                 */
                @Override
                public void apply(Builder builder) {
                    builder.minSize(mongoDBProperties.getMinSize()).maxSize(mongoDBProperties.getMaxSize())
                        .maxWaitTime(mongoDBProperties.getMaxWaitTime(), TimeUnit.MILLISECONDS)
                        .maxConnectionLifeTime(mongoDBProperties.getMaxConnectionLifeTime(), TimeUnit.MILLISECONDS)
                        .maxConnectionIdleTime(mongoDBProperties.getMaxConnectionIdleTime(), TimeUnit.MILLISECONDS);
                }
            }).applyToServerSettings(new Block<ServerSettings.Builder>() {

                /**
                 * Apply some logic to the value.
                 *
                 * @param builder
                 *            the value to apply to
                 */
                @Override
                public void apply(ServerSettings.Builder builder) {
                    builder.heartbeatFrequency(mongoDBProperties.getHeartbeatFrequency(), TimeUnit.MILLISECONDS);
                }
            }).applyToSocketSettings(new Block<SocketSettings.Builder>() {

                /**
                 * Apply some logic to the value.
                 *
                 * @param builder
                 *            the value to apply to
                 */
                @Override
                public void apply(SocketSettings.Builder builder) {
                    builder.connectTimeout(mongoDBProperties.getConnectTimeout(), TimeUnit.MILLISECONDS)
                        .readTimeout(mongoDBProperties.getReadTimeout(), TimeUnit.MILLISECONDS);
                }
            }).build();
        return MongoClients.create(mongoClientSettings);
    }

    @Override
    @Bean
    public MongoTemplate mongoTemplate() {
        // 覆盖 insertDocumentList 方法, 以便设置 insertMany 的 ordered = false
        return new MongoTemplate(mongoDbFactory(), mappingMongoConverter()) {

            @Nullable
            private WriteConcern writeConcern;

            @Override
            public void setWriteConcern(@Nullable WriteConcern writeConcern) {
                super.setWriteConcern(writeConcern);
                this.writeConcern = writeConcern;
            }

            @Override
            protected List<Object> insertDocumentList(final String collectionName, final List<Document> documents) {
                if (documents.isEmpty()) {
                    return Collections.emptyList();
                }
                execute(collectionName, collection -> {

                    MongoAction mongoAction = new MongoAction(writeConcern, MongoActionOperation.INSERT_LIST,
                        collectionName, null, null, null);
                    WriteConcern writeConcernToUse = prepareWriteConcern(mongoAction);

                    if (writeConcernToUse == null) {
                        collection.insertMany(documents, new InsertManyOptions().ordered(false));
                    } else {
                        collection.withWriteConcern(writeConcernToUse).insertMany(documents,
                            new InsertManyOptions().ordered(false));
                    }
                    return null;
                });

                return MappedDocument.toIds(documents);
            }
        };
    }

    @Override
    @Bean
    public MappingMongoConverter mappingMongoConverter() {
        DbRefResolver dbRefResolver = new DefaultDbRefResolver(mongoDbFactory);
        MappingMongoConverter converter = new MappingMongoConverter(dbRefResolver, mongoMappingContext);
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        converter.afterPropertiesSet();
        return converter;
    }

    /**
     * Return the name of the database to connect to.
     *
     * @return must not be {@literal null}.
     */
    @Override
    protected String getDatabaseName() {
        return mongoDBProperties.getDbName();
    }
}

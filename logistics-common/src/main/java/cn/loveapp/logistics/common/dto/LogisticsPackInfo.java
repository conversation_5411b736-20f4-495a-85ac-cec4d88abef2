package cn.loveapp.logistics.common.dto;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;
import cn.loveapp.logistics.common.utils.ConvertUtil;
import cn.loveapp.logistics.common.utils.ElasticsearchUtil;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 物流单包裹信息
 *
 * <AUTHOR>
 * @Date 2023/7/1 18:15
 */
@Data
@ApiModel
public class LogisticsPackInfo {

    @ApiModelProperty(value = "用户id")
    @JSONField(name = "seller_id")
    @JsonProperty("seller_id")
    private String sellerId;

    @ApiModelProperty(value = "用户nick")
    @JSONField(name = "seller_nick")
    @JsonProperty("seller_nick")
    private String sellerNick;

    @ApiModelProperty(value = "平台id")
    @JSONField(name = "store_id")
    @JsonProperty("store_id")
    private String storeId;

    @ApiModelProperty(value = "应用")
    @JSONField(name = "app_name")
    @JsonProperty("app_name")
    private String appName;


    @ApiModelProperty(value = "运单号")
    @JSONField(name = "out_sid")
    @JsonProperty("out_sid")
    private String outSid;

    @ApiModelProperty(value = "包裹类型")
    @JSONField(name = "pack_type")
    @JsonProperty("pack_type")
    private LogisticsPackType packType;

    @ApiModelProperty(value = "物流公司code")
    @JSONField(name = "company_code")
    @JsonProperty("company_code")
    private String companyCode;

    @ApiModelProperty(value = "物流公司name")
    @JSONField(name = "company_name")
    @JsonProperty("company_name")
    private String companyName;

    @ApiModelProperty(value = "物流来源平台")
    @JSONField(name = "logistics_store_id")
    @JsonProperty("logistics_store_id")
    private String logisticsStoreId;

    @ApiModelProperty(value = "物流单处理状态")
    @JSONField(name = "process_status")
    @JsonProperty("process_status")
    private String processStatus;

    @ApiModelProperty(value = "最新物流轨迹状态")
    @JSONField(name = "last_trace_status")
    @JsonProperty("last_trace_status")
    private String lastTraceStatus;

    @ApiModelProperty(value = "最新物流轨迹描述")
    @JSONField(name = "last_trace_desc")
    @JsonProperty("last_trace_desc")
    private String lastTraceDesc;

    @ApiModelProperty(value = "最新物流轨迹发生时间")
    @JSONField(name = "last_trace_status_time")
    @JsonProperty("last_trace_status_time")
    private String lastTraceStatusTime;

    @ApiModelProperty(value = "业务信息")
    @JSONField(name = "business_info")
    @JsonProperty("business_info")
    private BusinessInfo businessInfo;

    @ApiModelProperty(value = "异常列表")
    @JSONField(name = "abnormal_list")
    @JsonProperty("abnormal_list")
    private List<AbnormalDetails> abnormalList;

    @ApiModelProperty(value = "异常类型历史")
    @JSONField(name = "abnormal_type_history")
    @JsonProperty("abnormal_type_history")
    private List<String> abnormalTypeHistory;

    @ApiModelProperty(value = "轨迹列表")
    @JSONField(name = "trace_list")
    @JsonProperty("trace_list")
    private TracesDTO traceList;

    @ApiModelProperty(value = "创建时间")
    @JSONField(name = "created")
    @JsonProperty("created")
    private String created;


    /**
     * 业务信息
     */
    @Data
    @ApiModel
    public static class BusinessInfo {

        @ApiModelProperty(value = "关联订单列表")
        @JSONField(name = "tid_list")
        private List<String> tidList;

        @ApiModelProperty(value = "关联售后单列表")
        @JSONField(name = "refund_list")
        private List<String> refundIdList;

        @ApiModelProperty(value = "订单发货时间")
        @JSONField(name = "consign_time")
        private String consignTime;

        @ApiModelProperty(value = "买家昵称")
        @JSONField(name = "buyer_nick")
        private String buyerNick;

        @ApiModelProperty(value = "买家openUid")
        @JSONField(name = "buyer_open_uid")
        private String buyerOpenUid;

        @ApiModelProperty(value = "业务订单信息")
        @JSONField(name = "order_infos")
        private List<OrderInfo> orderInfos;

    }

    @Data
    @ApiModel
    public static class OrderInfo {

        @ApiModelProperty(value = "订单号")
        @JSONField(name = "tid")
        private String tid;

        @ApiModelProperty(value = "卖家旗帜")
        @JSONField(name = "seller_flag")
        private Integer sellerFlag;

        @ApiModelProperty(value = "订单自定义旗帜")
        @JSONField(name = "order_ay_custom_flag")
        private Integer orderAyCustomFlag;

        @ApiModelProperty(value = "卖家备注")
        @JSONField(name = "seller_memo")
        private String sellerMemo;

        @ApiModelProperty(value = "买家留言")
        @JSONField(name = "buyer_message")
        private String buyerMessage;

        @ApiModelProperty(value = "是否存在退款")
        @JSONField(name = "is_refund")
        private Boolean isRefund;

        @ApiModelProperty(value = "退款创建时间")
        @JSONField(name = "refund_created_time")
        private LocalDateTime refundCreatedTime;

        @ApiModelProperty(value = "包裹信息")
        @JSONField(name = "sku_infos")
        private List<SkuInfo> skuInfos;

    }

    @Data
    @ApiModel
    public static class SkuInfo {
        @ApiModelProperty(value = "skuId")
        @JSONField(name = "sku_id")
        private String skuId;

        @ApiModelProperty(value = "sku名称")
        @JSONField(name = "sku_name")
        private String skuName;

        @ApiModelProperty(value = "sku图片url")
        @JSONField(name = "pic_url")
        private String picUrl;

        @ApiModelProperty(value = "sku外部编码")
        @JSONField(name = "outer_sku_id")
        private String outerSkuId;

        @ApiModelProperty(value = "sku数量")
        @JSONField(name = "num")
        private Integer num;
    }

    /**
     * 异常项实体
     */
    @Data
    @ApiModel
    public static class AbnormalDetails {

        @ApiModelProperty(value = "异常类型")
        @JSONField(name = "abnormal_type")
        private String abnormalType;

        @ApiModelProperty(value = "异常处理状态")
        @JSONField(name = "process_status")
        private String processStatus;

        @ApiModelProperty(value = "异常结束时间")
        @JSONField(name = "abnormal_end_time")
        private LocalDateTime abnormalEndTime;
    }


    /**
     * 生成包裹详情
     *
     * @param orderInfo
     * @param logisticsTraceInfos
     * @return
     */
    public static LogisticsPackInfo of(LogisticsOrderInfo orderInfo, List<LogisticsTraceInfo> logisticsTraceInfos, String traceSortDirection, boolean isDistinctWithStatusField, Boolean includeProcessedAbnormal) {
        if (Objects.isNull(orderInfo)) {
            return null;
        }
        LogisticsPackInfo logisticsPackInfo = new LogisticsPackInfo();
        BeanUtils.copyProperties(orderInfo, logisticsPackInfo);
        if (logisticsPackInfo.getCreated() == null) {
            // 兼容老数据
            logisticsPackInfo.setCreated(DateUtil.convertDatetoString(orderInfo.getGmtCreate()));
        } else {
            logisticsPackInfo.setCreated(DateUtil.convertDatetoString(orderInfo.getCreated()));
        }
        if (!Objects.isNull(orderInfo.getBusinessInfo())) {
            LogisticsOrderInfo.BusinessInfo orderBusinessInfo = orderInfo.getBusinessInfo();
            List<LogisticsOrderInfo.OrderInfo> orderInfoList = orderBusinessInfo.getOrderInfoList();
            BusinessInfo businessInfo = new BusinessInfo();
            logisticsPackInfo.setBusinessInfo(ConvertUtil.convert(orderBusinessInfo, BusinessInfo.class));
            businessInfo.setTidList(ElasticsearchUtil.toList(orderBusinessInfo.getTidList()));
            businessInfo.setRefundIdList(ElasticsearchUtil.toList(orderBusinessInfo.getRefundIdList()));
            businessInfo.setConsignTime(DateUtil.convertDatetoString(orderBusinessInfo.getConsignTime()));
            businessInfo.setBuyerNick(orderBusinessInfo.getBuyerNick());
            businessInfo.setBuyerOpenUid(orderBusinessInfo.getBuyerOpenUid());

            if (CollectionUtils.isNotEmpty(orderInfoList)) {
                List<OrderInfo> orderInfos = Lists.newArrayList();
                for (LogisticsOrderInfo.OrderInfo info : orderInfoList) {
                    OrderInfo businessOrderInfo = new OrderInfo();
                    businessOrderInfo.setTid(info.getTid());
                    businessOrderInfo.setSellerFlag(info.getSellerFlag());
                    businessOrderInfo.setOrderAyCustomFlag(info.getOrderAyCustomFlag());
                    businessOrderInfo.setSellerMemo(info.getSellerMemo());
                    businessOrderInfo.setBuyerMessage(info.getBuyerMessage());
                    businessOrderInfo.setIsRefund(BooleanUtils.isTrue(info.getIsRefund()));
                    businessOrderInfo.setRefundCreatedTime(info.getRefundCreatedTime());

                    List<LogisticsOrderInfo.OrderSkuInfo> orderSkuInfoList = info.getSkuInfos();
                    List<SkuInfo> skuInfos = Lists.newArrayList();
                    if (CollectionUtils.isNotEmpty(orderSkuInfoList)) {
                        for (LogisticsOrderInfo.OrderSkuInfo orderSkuInfo : orderSkuInfoList) {
                            SkuInfo skuInfo = new SkuInfo();
                            skuInfo.setSkuId(orderSkuInfo.getSkuId());
                            skuInfo.setSkuName(orderSkuInfo.getSkuName());
                            skuInfo.setPicUrl(orderSkuInfo.getPicUrl());
                            skuInfo.setOuterSkuId(orderSkuInfo.getOuterSkuId());
                            skuInfo.setNum(orderSkuInfo.getNum());
                            skuInfos.add(skuInfo);
                        }

                    }

                    businessOrderInfo.setSkuInfos(skuInfos);
                    orderInfos.add(businessOrderInfo);
                }

                businessInfo.setOrderInfos(orderInfos);
            }

            logisticsPackInfo.setBusinessInfo(businessInfo);
        }
        logisticsPackInfo.setLastTraceStatus(orderInfo.getLastAction());
        logisticsPackInfo.setLastTraceDesc(orderInfo.getLastTraceDesc());
        logisticsPackInfo.setLastTraceStatusTime(DateUtil.convertDatetoString(orderInfo.getLastActionModified()));
        // 生成返回的异常项
        logisticsPackInfo.generalAbnormalList(orderInfo, includeProcessedAbnormal);

        //生成轨迹数据
        logisticsPackInfo.generalTracesDTO(logisticsPackInfo.getOutSid(), logisticsTraceInfos, traceSortDirection, isDistinctWithStatusField);

        logisticsPackInfo.setPackType(LogisticsPackType.getPackType(orderInfo));
        logisticsPackInfo.setAbnormalTypeHistory(orderInfo.getLogisticsAbnormalTypeHistory());
        return logisticsPackInfo;
    }

    /**
     * 生成轨迹列表
     *
     * @param outSid
     * @param logisticsTraceInfos
     */
    private void generalTracesDTO(String outSid, List<LogisticsTraceInfo> logisticsTraceInfos, String traceSortDirection, boolean isDistinctWithStatusField) {
        if (logisticsTraceInfos == null || StringUtils.isEmpty(outSid)) {
            return;
        }
        Map<String, TracesDTO> tracesMap = TracesDTO.of(logisticsTraceInfos, traceSortDirection, isDistinctWithStatusField);
        this.traceList = tracesMap.get(outSid);

    }

    /**
     * 生成异常列表
     *
     * @param orderInfo
     */
    private void generalAbnormalList(LogisticsOrderInfo orderInfo, Boolean includeProcessedAbnormal) {
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = orderInfo.getLogisticsAbnormalInfo();
        if (logisticsAbnormalInfo == null) {
            return;
        }
        String processStatus = logisticsAbnormalInfo.getProcessStatus();
        this.processStatus = processStatus;
        if (StringUtils.isNotEmpty(processStatus) && AbnormalProcessStatus.PROCESSED.value().equals(processStatus)
            && BooleanUtils.isNotTrue(includeProcessedAbnormal)) {
            // 总状态为已处理，表示没有异常
            return;
        }
        Map<String, LogisticsOrderInfo.AbnormalDetails> abnormalDetailsMap = orderInfo.convertToAbnormalMap(includeProcessedAbnormal);
        if (Objects.isNull(abnormalDetailsMap) || abnormalDetailsMap.size() == 0) {
            // 不存在异常
            return;
        }

        if (this.abnormalList == null) {
            this.abnormalList = new ArrayList<>();
        }

        abnormalDetailsMap.forEach((s, abnormalDetails) -> {
            AbnormalDetails details = new AbnormalDetails();
            details.setAbnormalType(s);
            details.setProcessStatus(abnormalDetails.getProcessStatus());
            details.setAbnormalEndTime(DateUtil.parseDate(abnormalDetails.getAbnormalEndTime()));
            this.abnormalList.add(details);
        });
    }


}

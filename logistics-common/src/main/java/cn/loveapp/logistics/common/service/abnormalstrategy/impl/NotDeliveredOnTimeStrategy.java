package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static cn.loveapp.logistics.api.constant.AyLogisticsStatus.*;
import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 物流异常策略 超时未签收
 * <p>
 * LogisticsAbnormalType.NOT_DELIVERED_ON_TIME
 *
 * <AUTHOR>
 * @Date 2023/6/21 10:41
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class NotDeliveredOnTimeStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(NotDeliveredOnTimeStrategy.class);


    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String appName = logisticsOrderInfo.getAppName();
        if (LogisticsUtil.isTradeERP(appName)) {
            // erp(无需处理此类异常 跳过)
            return new ExecuteResult();
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails notDeliveredOnTime = logisticsAbnormalInfo.getNotDeliveredOnTime();
        if (Objects.isNull(notDeliveredOnTime)) {
            notDeliveredOnTime = new LogisticsOrderInfo.AbnormalDetails();
        }

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();

        boolean hasAbnormal;
        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            boolean delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
            List<String> abnormalStatus = logisticsStatusList.stream().filter(status -> {
                return status.equals(TIMEOUT_WITHOUT_SIGNATURE.value()) || status.equals(TIMEOUT_AT_MAILBOX_OR_SERVICE_POString.value());
            }).collect(Collectors.toList());
            hasAbnormal = !delivered && CollectionUtils.isNotEmpty(abnormalStatus);
        } else {
            hasAbnormal = false;
        }

        boolean isUpdate = LogisticsOrderInfo.generalAbnormalDetails(hasAbnormal, notDeliveredOnTime, null);

        if (hasAbnormal || isUpdate) {
            LOGGER.logInfo("执行策略：【超时未签收】，判断结果：" + hasAbnormal + ", 是否变更：" + isUpdate);
        }
        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setNotDeliveredOnTime(notDeliveredOnTime);
        }
        logisticsOrderInfo.checkAndSetMainAbnormalInfo(hasAbnormal, isUpdate);
        return new ExecuteResult(isUpdate, null);
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getNotDeliveredOnTimeCount();
        if (countAll == null) {
            countAll = 0;
        }
        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setNotDeliveredOnTimeCount(countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        // 异常存在且未处理
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.notDeliveredOnTimeIsExists, true))
            .mustNot(termQuery(EsFields.notDeliveredOnTimeProcessStatus, AbnormalProcessStatus.PROCESSED));

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(EsFields.notDeliveredOnTimeProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(EsFields.notDeliveredOnTimeProcessStatus))));
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {

        LogisticsOrderInfo.AbnormalDetails notDeliveredOnTime = logisticsAbnormalInfo.getNotDeliveredOnTime();
        if (Objects.isNull(notDeliveredOnTime)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(notDeliveredOnTime.getProcessStatus(), newProcessStatus,
            appName)) {
            notDeliveredOnTime.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setNotDeliveredOnTime(notDeliveredOnTime);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails notDeliveredOnTime = logisticsAbnormalInfo.getNotDeliveredOnTime();
        return !Objects.isNull(notDeliveredOnTime) && BooleanUtils.isTrue(notDeliveredOnTime.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(notDeliveredOnTime.getProcessStatus());
    }

    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.NOT_DELIVERED_ON_TIME.value();
    }

    @Override
    public LocalDateTime getAbnormalDeadline(LocalDateTime checkTime) {
        return null;
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_NOT_DELIVERED_ON_TIME_PROMPT, true, Boolean.class);
    }
}

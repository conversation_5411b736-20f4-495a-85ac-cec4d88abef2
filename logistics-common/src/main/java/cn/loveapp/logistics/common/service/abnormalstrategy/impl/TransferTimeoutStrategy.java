package cn.loveapp.logistics.common.service.abnormalstrategy.impl;

import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.LogisticsAbnormalCheckConfig;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import static org.elasticsearch.index.query.QueryBuilders.*;

/**
 * 中转超时: 定义为揽收并且第一次物流更新后，签收前，物流长时间未离开某节点
 */

@Component
@LogisticsAbnormalStrategyAnnotation
public class TransferTimeoutStrategy extends AbstractLogisticsAbnormalStrategy{

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TransferTimeoutStrategy.class);

    @Autowired
    protected LogisticsAbnormalCheckConfig checkConfig;

    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {

        Date lastActionModified = logisticsOrderInfo.getLastActionModified();
        String appName = logisticsOrderInfo.getAppName();
        String lastAction = logisticsOrderInfo.getLastAction();
        if (Objects.isNull(lastActionModified) || StringUtils.isEmpty(lastAction)) {
            return new ExecuteResult(false, getAbnormalDeadline(null));
        }

        if (LogisticsUtil.isTradeERP(appName)) {
            boolean transferTimeoutEnable =
                getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_TRANSFER_TIMEOUT, true, Boolean.class);
            if (!transferTimeoutEnable) {
                return new ExecuteResult();
            }
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;

        Integer checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_TRANSFER_TIMEOUT, 12, Integer.class);

        // 上次轨迹变更时间
        LocalDateTime lastActionModifiedTime = DateUtil.parseDate(lastActionModified);
        // N小时未更新
        LocalDateTime deadline = lastActionModifiedTime.plusHours(checkTime);
        boolean updateAfterCheckTime = deadline.isBefore(now);

        Set<String> logisticsStatusList = logisticsOrderInfo.getLogisticsStatusList();

        // 已签收
        boolean delivered = false;

        boolean lastActionIsInTransit = AyLogisticsStatus.IN_TRANSIT_LIST.contains(lastAction);

        if (CollectionUtils.isNotEmpty(logisticsStatusList)) {
            delivered = logisticsStatusList.stream().anyMatch(AyLogisticsStatus.DELIVERED_LIST::contains);
        }

        // 揽收后签收前轨迹超N小时未更新 最后一次更新轨迹时间 + N小时 < 当前时间 && 处于在途中 && 物流轨迹未签收
        // (不校验揽收状态原因: 存在中转中的状态一定是已揽收，不校验揽收状态，防止揽收消息丢失导致该异常判断不上)
        boolean checkTransferTimeout = updateAfterCheckTime && lastActionIsInTransit && !delivered;

        LogisticsOrderInfo.AbnormalDetails transferTimeout = logisticsAbnormalInfo.getTransferTimeout();
        if (Objects.isNull(transferTimeout)) {
            transferTimeout = new LogisticsOrderInfo.AbnormalDetails();
        }

        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkTransferTimeout, transferTimeout, deadline);
        if (checkTransferTimeout || isUpdate) {
            LOGGER.logInfo(logisticsOrderInfo.getOutSid() + " 执行策略：【揽收并且第一次物流更新后签收前" + checkTime + "未更新】，判断结果："
                + checkTransferTimeout + ", 是否变更：" + isUpdate);
        }

        if (isUpdate) {
            // 存在变化，更新存单
            logisticsAbnormalInfo.setTransferTimeout(transferTimeout);
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkTransferTimeout, isUpdate);
        if (isUpdate && checkTransferTimeout) {
            appendLogisticsAbnormalTypeHistory(logisticsOrderInfo, getAbnormalType());
        }

        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModifiedTime));
    }


    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.TRANSFER_TIMEOUT.value();
    }


    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getTransferTimeoutCount();
        if (countAll == null) {
            countAll = 0;
        }

        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setTransferTimeoutCount(countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.transferTimeoutIsExists, true))
            .must(existsQuery(EsFields.consignTime))
            .mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(EsFields.transferTimeoutProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(EsFields.transferTimeoutProcessStatus))));
        }
        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails transferTimeout = logisticsAbnormalInfo.getTransferTimeout();
        if (Objects.isNull(transferTimeout)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(transferTimeout.getProcessStatus(), newProcessStatus, appName)) {
            transferTimeout.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setTransferTimeout(transferTimeout);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getTransferTimeout();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }

    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime lastActionModifiedTime) {
        int checkTimeMin = checkConfig.getTransferTimeoutMinCheckTime();
        int checkTimeMax = checkConfig.getTransferTimeoutMaxCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, lastActionModifiedTime);
    }
}

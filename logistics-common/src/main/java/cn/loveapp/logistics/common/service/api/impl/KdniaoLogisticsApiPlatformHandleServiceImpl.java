package cn.loveapp.logistics.common.service.api.impl;

import java.util.List;

import cn.loveapp.common.dto.LogisticsCallbackDataDTO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.kdniao.api.domain.Trace;
import com.kdniao.api.request.ApiDistRequest;
import com.kdniao.api.request.EbusinessOrderHandleRequest;
import com.kdniao.api.request.LogisticNumberRecognitionRequest;
import com.kdniao.api.response.ApiDistResponse;
import com.kdniao.api.response.EbusinessOrderHandleResponse;
import com.kdniao.api.response.LogisticNumberRecognitionResponse;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.platformsdk.kdniao.KdniaoSDKService;
import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.common.constant.LogisticsPackType;
import cn.loveapp.logistics.common.dao.mongo.LogisticsOrderInfoDao;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.dto.request.AyLogisticNumberRecognitionRequest;
import cn.loveapp.logistics.common.dto.request.AySearchLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.AySubscribeLogisticsTraceRequest;
import cn.loveapp.logistics.common.dto.request.LogisticsMonitoringLogRequest;
import cn.loveapp.logistics.common.dto.response.AyLogisticNumberRecognitionResponse;
import cn.loveapp.logistics.common.dto.response.AySearchLogisticsTraceResponse;
import cn.loveapp.logistics.common.dto.response.AySubscribeLogisticsTraceResponse;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.common.service.AyLogisticsQuotaService;
import cn.loveapp.logistics.common.service.api.LogisticsApiPlatformHandleService;

/**
 * 快递鸟物流接口实现类
 *
 * <AUTHOR>
 * @Date 2023/5/30 15:20
 */
@Service
public class KdniaoLogisticsApiPlatformHandleServiceImpl implements LogisticsApiPlatformHandleService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(KdniaoLogisticsApiPlatformHandleServiceImpl.class);

    @Autowired
    private KdniaoSDKService kdniaoSDKService;

    @Autowired
    private LogisticsOrderInfoDao logisticsOrderInfoDao;

    @Autowired
    private AyLogisticsQuotaService ayLogisticsQuotaService;

    /**
     * 需要校验customName字段的 物流公司编码集合
     */
    @Value("${logistics.service.kdniao.validCustomName.cpCodes:SF}")
    private List<String> kdniaoValidCustomNameCpCodes = Lists.newArrayList();

    @Override
    public String getDispatcherId() {
        return CommonLogisticsConstants.PLATFORM_KDNIAO;
    }

    @Override
    public AySubscribeLogisticsTraceResponse subscribeLogisticsTrace(AySubscribeLogisticsTraceRequest request, String logisticsStoreId, String logisticsAppName) {
        AySubscribeLogisticsTraceResponse response = new AySubscribeLogisticsTraceResponse();
        LogisticsOrderSubscribeDTO logisticsHandle = request.getLogisticsHandle();
        if (logisticsHandle == null) {
            LOGGER.logError("快递鸟订阅失败，参数为空");
            response.setSuccess(Boolean.FALSE);
            return response;
        }

        String sellerId = logisticsHandle.getSellerId();
        String storeId = logisticsHandle.getStoreId();
        String outSid = logisticsHandle.getOutSid();
        String appName = logisticsHandle.getAppName();
        String customerName = logisticsHandle.getCustomerName();
        String logisticsCompanyCode = logisticsHandle.getLogisticsCompanyCode();

        if (kdniaoValidCustomNameCpCodes.contains(logisticsCompanyCode)) {
            if (StringUtils.isEmpty(customerName)) {
                response.setSuccess(Boolean.FALSE);
                response.setErrorMsg("物流公司code为：" + logisticsCompanyCode + "时手机号不能为空: 运单号" + outSid);
                return response;
            }
        }

        LogisticsOrderInfo lastLogisticsOrderInfo = logisticsOrderInfoDao.queryByOutSidAndSeller(outSid, sellerId, storeId, appName);
        LogisticsPackType packType = LogisticsPackType.getPackType(logisticsHandle);

        //需要消耗额度：查询是否有订阅记录，存在则不消耗
        //无需消耗额度：指定时无需消耗额度
        boolean isNeedDeductionQuota = request.isNeedDeductionQuota();
        if (isNeedDeductionQuota) {
            if (lastLogisticsOrderInfo != null && lastLogisticsOrderInfo.getSubscribeInfo() != null) {
                LogisticsOrderInfo.SubscribeInfo subscribeInfo = lastLogisticsOrderInfo.getSubscribeInfo();
                if (BooleanUtils.isTrue(subscribeInfo.getIsSuccess()) && logisticsStoreId.equals(lastLogisticsOrderInfo.getSaveLogisticsStoreId())) {
                    // 已订阅过快递鸟无需扣额度
                    isNeedDeductionQuota = false;
                }
                packType = LogisticsPackType.getPackType(lastLogisticsOrderInfo);
            }
        }

        Integer monitoringNumWithholding =
            ayLogisticsQuotaService.withholdingLogisticQuota(logisticsHandle, isNeedDeductionQuota);
        if (monitoringNumWithholding < 0) {
            response.setSuccess(Boolean.FALSE);
            response.setErrorMsg("余额不足，订阅失败");
            return response;
        }

        ApiDistRequest apiDistRequest = new ApiDistRequest();
        apiDistRequest.setLogisticCode(outSid);
        apiDistRequest.setShipperCode(logisticsCompanyCode);
        apiDistRequest.setCustomerName(customerName);

        LogisticsCallbackDataDTO callbackData = new LogisticsCallbackDataDTO();
        callbackData.setSellerId(logisticsHandle.getSellerId());
        callbackData.setAppName(logisticsHandle.getAppName());
        callbackData.setStoreId(logisticsHandle.getStoreId());
        apiDistRequest.setCallback(callbackData.generateCallbackStr());

        boolean isSubscribeSuccess = true;
        try {
            ApiDistResponse apiDistResponse = kdniaoSDKService.excute(apiDistRequest, logisticsAppName);
            if (apiDistResponse == null || !apiDistResponse.isSuccess()) {
                isSubscribeSuccess = false;
            }
        } catch (Exception e) {
            LOGGER.logError("调用快递鸟订阅接口失败：" + e.getMessage(), e);
            isSubscribeSuccess = false;
        }
        response.setSuccess(isSubscribeSuccess);

        // 成功或失败发送消息回退额度或确认消费
        ayLogisticsQuotaService.logisticQuotaConsumeConfirm(logisticsHandle, isSubscribeSuccess, isNeedDeductionQuota, monitoringNumWithholding,packType,LogisticsMonitoringLogRequest.MONITORING_TYPE_SUBSCRIBE);
        return response;
    }

    @Override
    public AySearchLogisticsTraceResponse searchLogisticsTrace(AySearchLogisticsTraceRequest request, UserInfoDTO userInfoDTO, String logisticsStoreId, String appName) throws LogisticsHandlesException{
        String ownerSellerNick = null;
        String ownerSellerId = null;
        String ownerAppName = null;
        String ownerStoreId = null;
        switch (request.getSourceApp().value()) {
            case LOGIN_USER:
                //淘宝交易扣除账号为当前登录账号，使用当前登录人作为扣除余额的用户
                ownerSellerId = userInfoDTO.getSellerId();
                ownerStoreId = userInfoDTO.getStoreId();
                ownerAppName = userInfoDTO.getAppName();
                ownerSellerNick = userInfoDTO.getNick();
                break;
            case TARGET_USER:
                //多平台使用的是订阅人的信息，根据订阅人去查询其对应的爱用账号，使用爱用账号得余额进行扣除
                ownerSellerNick = request.getSellerNick();
                ownerSellerId = request.getSellerId();
                ownerAppName = request.getAppName();
                ownerStoreId = request.getStoreId();
                break;
        }
        AySearchLogisticsTraceResponse response = new AySearchLogisticsTraceResponse();
        LogisticsOrderSubscribeDTO logisticsOrderSubscribeDTO = new LogisticsOrderSubscribeDTO();
        logisticsOrderSubscribeDTO.setSellerNick(ownerSellerNick);
        logisticsOrderSubscribeDTO.setSellerId(ownerSellerId);
        logisticsOrderSubscribeDTO.setAppName(ownerAppName);
        logisticsOrderSubscribeDTO.setStoreId(ownerStoreId);
        logisticsOrderSubscribeDTO.setOutSid(request.getOutSid());
        logisticsOrderSubscribeDTO.setLogisticsCompanyCode(request.getLogisticsCompanyCode());
        logisticsOrderSubscribeDTO.setSourceApp(request.getSourceApp() != null ? request.getSourceApp().name() : null);

        String logisticsCompanyCode = request.getLogisticsCompanyCode();
        String outSid = request.getOutSid();
        String customerName = request.getCustomerName();
        if (kdniaoValidCustomNameCpCodes.contains(logisticsCompanyCode)) {
            if (StringUtils.isEmpty(customerName)) {
                throw new LogisticsHandlesException("物流公司code为：" + logisticsCompanyCode + "时手机号不能为空: 运单号" + outSid);
            }
        }

        Integer monitoringNumWithholding =
            ayLogisticsQuotaService.withholdingLogisticQuota(logisticsOrderSubscribeDTO, request.isNeedDeductionQuota());
        if (monitoringNumWithholding < 0) {
            throw new LogisticsHandlesException("快递鸟余额查询失败，参数为空");
        }
        EbusinessOrderHandleRequest handleRequest = new EbusinessOrderHandleRequest();
        handleRequest.setLogisticCode(outSid);
        handleRequest.setShipperCode(logisticsCompanyCode);
        handleRequest.setCustomerName(customerName);
        EbusinessOrderHandleResponse handleResponse = kdniaoSDKService.excute(handleRequest, appName);
        if (!handleResponse.isSuccess()) {
            LOGGER.logError("调用查询api错误：" + JSON.toJSONString(handleResponse));
            // 回滚消费
            ayLogisticsQuotaService.logisticQuotaConsumeConfirm(logisticsOrderSubscribeDTO, false,
                request.isNeedDeductionQuota(), monitoringNumWithholding, null,
                LogisticsMonitoringLogRequest.MONITORING_TYPE_LOOK);
            throw new LogisticsHandlesException("物流轨迹查询api失败");
        } else if (CollectionUtils.isNotEmpty(handleResponse.getTraces())) {
            List<Trace> traces = handleResponse.getTraces();
            for (Trace trace : traces) {
                LogisticsInfoDTO logisticsInfo = new LogisticsInfoDTO();
                logisticsInfo.setLogisticsStoreId(getDispatcherId());
                logisticsInfo.setOutSid(handleResponse.getLogisticCode());
                logisticsInfo.setCompanyCode(handleResponse.getShipperCode());
                logisticsInfo.setModified(DateUtil.parseDateString(trace.getAcceptTime()));
                logisticsInfo.setStatus(trace.getAction());
                logisticsInfo.setAction(trace.getAction());
                logisticsInfo.setDesc(trace.getAcceptStation());
                response.addLogisticsInfo(logisticsInfo);
            }

            //确认消费
            ayLogisticsQuotaService.logisticQuotaConsumeConfirm(logisticsOrderSubscribeDTO, true,
                request.isNeedDeductionQuota(), monitoringNumWithholding, null,
                LogisticsMonitoringLogRequest.MONITORING_TYPE_LOOK);
        }
        return response;
    }

    @Override
    public AyLogisticNumberRecognitionResponse recognitionLogisticNumber(
        AyLogisticNumberRecognitionRequest ayLogisticNumberRecognitionRequest, String logisticsStoreId,
        String appName) {
        String outSid = ayLogisticNumberRecognitionRequest.getOutSid();
        LogisticNumberRecognitionRequest request = new LogisticNumberRecognitionRequest();
        request.setLogisticCode(outSid);
        LogisticNumberRecognitionResponse response = kdniaoSDKService.excute(request, appName);
        if (response == null) {
            LOGGER.logInfo(outSid, logisticsStoreId, "调用物流单号识别接口异常, 返回值为null");
            return null;
        }
        if (!BooleanUtils.isTrue(response.getSuccess())) {
            LOGGER.logInfo(outSid, logisticsStoreId, "调用物流单号识别接口失败, 原因: " + response.getReason());
            return null;
        }

        List<LogisticNumberRecognitionResponse.Shippers> shippers = response.getShippers();
        if (CollectionUtils.isEmpty(shippers)) {
            LOGGER.logInfo(outSid, logisticsStoreId, "没有识别出对应的物流公司");
            return null;
        }

        // 当平台返回多个时以第一个为准
        LogisticNumberRecognitionResponse.Shippers shipper = shippers.get(0);
        AyLogisticNumberRecognitionResponse ayLogisticNumberRecognitionResponse =
            new AyLogisticNumberRecognitionResponse();
        ayLogisticNumberRecognitionResponse.setCompanyCode(shipper.getShipperCode());
        ayLogisticNumberRecognitionResponse.setCompanyName(shipper.getShipperName());
        return ayLogisticNumberRecognitionResponse;
    }
}

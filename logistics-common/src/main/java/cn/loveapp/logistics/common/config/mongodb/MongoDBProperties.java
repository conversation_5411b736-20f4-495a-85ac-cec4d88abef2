//package cn.loveapp.logistics.common.config.mongodb;
//
//import java.util.List;
//
//import org.apache.commons.lang3.BooleanUtils;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//
//import com.mongodb.WriteConcern;
//
//import lombok.Data;
//
///**
// * MongoDBProperties
// *
// * <AUTHOR>
// * @date 2022/1/24
// */
//@Data
//@ConfigurationProperties("mongodb.config")
//public class MongoDBProperties {
//    private List<String> hosts;
//    private String srvHost;
//    private int port;
//    private String dbName;
//    private String account;
//    private String password;
//    /**
//     * mongo cluster options config
//     */
//    private int minConnectionsPerHost;
//    private int connectionsPerHost;
//    // private int maxConnectionIdleTime;
//    private int socketTimeout;
//    // private int connectTimeout;
//    // private int maxConnectionLifeTime;
//    // private int maxWaitTime;
//
//    private int minSize;
//    private int maxSize;
//    private int maxWaitTime;
//    private int maxConnectionLifeTime;
//    private int maxConnectionIdleTime;
//    private int heartbeatFrequency;
//    private int connectTimeout;
//    private int readTimeout;
//    private int retryWrites;
//
//    private String writeConcern;
//
//    public Boolean getRetryWrites() {
//        return BooleanUtils.toBoolean(retryWrites);
//    }
//
//    private static final String WRITE_CONCERN_W1 = "W1";
//    private static final String WRITE_CONCERN_MAJORITY = "MAJORITY";
//
//    /**
//     * 获取mongo的writeConcern配置
//     *
//     * @return
//     */
//    public WriteConcern getWriteConcern() {
//        switch (writeConcern) {
//            case WRITE_CONCERN_W1:
//                return WriteConcern.W1;
//            default:
//                return WriteConcern.MAJORITY;
//        }
//    }
//
//}

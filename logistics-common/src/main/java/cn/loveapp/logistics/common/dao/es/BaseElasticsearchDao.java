package cn.loveapp.logistics.common.dao.es;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.elasticsearch.ElasticsearchConfiguration;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.entity.es.ElasticsearchEntity;
import cn.loveapp.logistics.common.entity.es.LogisticsOrderInfoSearchES;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.DocWriteResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Requests;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.metrics.NumericMetricsAggregation;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.fetch.subphase.FetchSourceContext;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.data.elasticsearch.ElasticsearchException;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ResultsMapper;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQuery;
import org.springframework.data.elasticsearch.core.query.UpdateQueryBuilder;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;

/**
 * AbstractESDao
 *
 * <AUTHOR>
 * @date 2020/2/18
 */
public abstract class BaseElasticsearchDao<T extends ElasticsearchEntity> {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(BaseElasticsearchDao.class);
    protected final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(LogisticsOrderInfoSearchES.GMT_FORMATER);
    protected final DateTimeFormatter minuteSecondFormatter = DateTimeFormatter.ofPattern(LogisticsOrderInfoSearchES.MINUTE_SECOND_FORMATER);

    protected final String _DOC = "_doc";

    protected ElasticsearchConfiguration elasticsearchConfiguration;

    protected RestHighLevelClient client;

    protected ResultsMapper resultsMapper;

    protected ElasticsearchOperations operations;

    protected ObjectMapper notNullObjectMapper;

    public BaseElasticsearchDao(ElasticsearchConfiguration configuration, ElasticsearchOperations operations,
                                RestHighLevelClient client, ResultsMapper mapper) {
        this.elasticsearchConfiguration = configuration;
        this.operations = operations;
        this.client = client;
        this.resultsMapper = mapper;
        notNullObjectMapper = new ObjectMapper();
        notNullObjectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        notNullObjectMapper.setTimeZone(TimeZone.getTimeZone("GMT+8"));
    }

    public ElasticsearchOperations getOperations() {
        return operations;
    }

    public RestHighLevelClient getClient() {
        return client;
    }

    public abstract String getIndexName(@NotNull T entity);


    public abstract String getIndexName();

    public abstract String getRouting(@NotNull T entity);


    protected String getRouting(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> routerArr = new HashSet<>();
        String router;
        TargetSellerInfo sellerInfo;
        for (int i = 0; i < sellerInfoList.size(); i++) {
            sellerInfo = sellerInfoList.get(i);
            router = getHashRoutingKey(sellerInfo.getTargetSellerId(), sellerInfo.getTargetCorpId());
            if (StringUtils.isNotEmpty(router)) {
                routerArr.add(router);
            }
        }
        String join = StringUtils.join(routerArr, ",");
        return StringUtils.isEmpty(join) ? null : join;
    }

    protected String getHashRoutingKey(@NotNull String sellerId, String corpId) {
        return sellerId;
    }


    protected String[] getIndexName(@NotNull List<TargetSellerInfo> sellerInfoList) {
        Set<String> indexNameArr = new HashSet<>();
        String indexName;
        TargetSellerInfo sellerInfo;
        for (int i = 0; i < sellerInfoList.size(); i++) {
            sellerInfo = sellerInfoList.get(i);
            indexName = getIndexName(sellerInfo.getTargetSellerId(), sellerInfo.getTargetCorpId());
            indexNameArr.add(indexName);
        }
        return indexNameArr.toArray(new String[0]);
    }

    public String getIndexName(@NotNull String sellerId, String corpId) {
        if (StringUtils.isEmpty(sellerId)) {
            //sellerid为空时，默认查询所有索引
            return LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX + "*";
        }
        return operations.getPersistentEntityFor(LogisticsOrderInfoSearchES.class).getIndexName()
            + (Math.abs(getHashRoutingKey(sellerId, corpId).hashCode())
            % elasticsearchConfiguration.getIndexNum());
    }

    public long count(T entity, NativeSearchQuery query) {
        CountRequest countRequest = new CountRequest(getIndexName(entity));
        countRequest.routing(getRouting(entity));
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query.getQuery());
        countRequest.source(searchSourceBuilder);
        try {
            CountResponse response = client.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while count for request: " + searchSourceBuilder.toString(), e);
        }
    }

    public long count(T entity, List<TargetSellerInfo> sellerInfoList, NativeSearchQuery query) {
        if (CollectionUtils.isEmpty(sellerInfoList)) {
            return count(entity, query);
        }
        String[] indexNameArr = getIndexName(sellerInfoList);
        String routingArrStr = getRouting(sellerInfoList);
        CountRequest countRequest = new CountRequest(indexNameArr);
        countRequest.routing(routingArrStr);
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query.getQuery());
        countRequest.source(searchSourceBuilder);
        try {
            CountResponse response = client.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while count for request: " + searchSourceBuilder.toString(), e);
        }
    }

    /**
     * 不指定索引和router
     *
     * @param query
     * @return
     */
    public long count(NativeSearchQuery query) {
        CountRequest countRequest = new CountRequest(LogisticsOrderInfoSearchES.INDEX_NAME_PREFIX + "*");
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query(query.getQuery());
        countRequest.source(searchSourceBuilder);
        try {
            CountResponse response = client.count(countRequest, RequestOptions.DEFAULT);
            return response.getCount();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while count for request: " + searchSourceBuilder.toString(), e);
        }
    }


    /**
     * 求和函数
     * 默认去取第一个聚合的 参数 ，目前只聚合了一个参数
     *
     * @param query
     * @param agg
     * @return
     */
    public Double aggWithSingleValue(NativeSearchQuery query, AggregationBuilder agg) {
        if (LOGGER.isDebugEnabled()) {
            LOGGER.logDebug(null, "-", " queryWaitSendTrades DSL=> " + query.toString());
        }
        return ((NumericMetricsAggregation.SingleValue) (aggs(query, agg).asList().get(0))).value();
    }

    /**
     * 获取响应返回后的 Aggregations
     *
     * @param query NativeSearchQuery
     * @param agg   AggregationBuilder
     * @return Aggregations
     */
    public Aggregations aggs(NativeSearchQuery query, AggregationBuilder... agg) {
        SearchSourceBuilder source = new SearchSourceBuilder()
            .query(query.getQuery())
            .size(0);
        for (AggregationBuilder aggItem : agg) {
            source.aggregation(aggItem);
        }

        SearchRequest request = new SearchRequest(query.getIndices().toArray(new String[0]));
        request.routing(query.getRoute());
        request.source(source);

        try {
            SearchResponse response = client.search(request, RequestOptions.DEFAULT);
            return response.getAggregations();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while sum for request: " + source.toString(), e);
        }

    }

    public void save(@NotNull T entity) {
        String indexName = getIndexName(entity);
        IndexRequest indexRequest = new IndexRequest(indexName);
        try {
            // 复制一个, 防止原始信息修改
            entity = (T) entity.clone();
            // 初始化默认值
            entity.initDefault();
            Date date = new Date();
            entity.setGmtCreate(date);
            entity.setGmtModified(date);
            indexRequest.id(entity.getId());
            indexRequest.routing(getRouting(entity));
            indexRequest.source(resultsMapper.getEntityMapper().mapToString(entity), Requests.INDEX_CONTENT_TYPE);
            client.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while index for request: " + indexRequest.toString(), e);
        }
    }

    /**
     * 依据entity的内容修改索引内容, 为null的属性不修改
     *
     * @param entity
     * @return
     */
    public int updateByIdWithNotNull(@NotNull T entity) {
        Assert.notNull(entity, "Cannot update 'null' entity.");
        // 复制一个, 防止原始信息修改
        entity = (T) entity.clone();
        // 只能初始化特殊值, 不能初始化默认值, 否则默认值会更新进去
        entity.initSpecial();
        String indexName = getIndexName(entity);
        UpdateRequest updateRequest = new UpdateRequest(indexName, entity.getId());
        try {
            entity.setGmtCreate(null);
            entity.setGmtModified(new Date());
            String doc = notNullObjectMapper.writeValueAsString(entity);
            updateRequest.routing(getRouting(entity)).doc(doc, XContentType.JSON).docAsUpsert(false);

            client.update(updateRequest, RequestOptions.DEFAULT);
        } catch (ElasticsearchStatusException e) {
            if (e.status() == RestStatus.NOT_FOUND) {
                return 0;
            }
            throw e;
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while update for request: " + updateRequest.toString(), e);
        }
        return 1;
    }

    /**
     * 依据entity的内容和fields指定的字段修改索引内容, fields指定的字段即使为null也修改
     *
     * @param entity
     * @param fields 要更新的字段
     * @return
     */
    public int updateByIdWithNull(@NotNull T entity, @NotNull List<String> fields) {
        Assert.notNull(entity, "Cannot update 'null' entity.");
        String indexName = getIndexName(entity);
        UpdateRequest updateRequest = new UpdateRequest(indexName, entity.getId());
        try {
            // 复制一个, 防止原始信息修改
            entity = (T) entity.clone();
            // 初始化默认值
            entity.initDefault();
            Map<String, Object> map = resultsMapper.getEntityMapper().mapObject(entity);
            map.keySet().retainAll(fields);
            map.remove(EsFields.gmtCreate);
            map.put(EsFields.gmtModified, formatter.format(LocalDateTime.now()));
            String doc = resultsMapper.getEntityMapper().mapToString(map);
            updateRequest.routing(getRouting(entity)).doc(doc, XContentType.JSON).docAsUpsert(false);

            client.update(updateRequest, RequestOptions.DEFAULT);
        } catch (ElasticsearchStatusException e) {
            if (e.status() == RestStatus.NOT_FOUND) {
                return 0;
            }
            throw e;
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while update for request: " + updateRequest.toString(), e);
        }
        return 1;
    }

    /**
     * 依据entity的内容和fields指定的字段批量修改索引内容, fields指定的字段即使为null也修改
     *
     * @param entity
     * @param fields fields
     * @param ids
     * @return
     */
    public int updateByIds(@NotNull T entity, @NotNull List<String> fields, @NotEmpty List<String> ids) {
        String indexName = getIndexName(entity);
        String routing = getRouting(entity);
        List<UpdateQuery> queries = Lists.newArrayList();

        // 复制一个, 防止原始信息修改
        entity = (T) entity.clone();
        // 初始化默认值
        entity.initDefault();
        Map<String, Object> map = resultsMapper.getEntityMapper().mapObject(entity);
        map.keySet().retainAll(fields);
        map.remove(EsFields.gmtCreate);
        map.put(EsFields.gmtModified, formatter.format(LocalDateTime.now()));
        String doc;
        try {
            doc = resultsMapper.getEntityMapper().mapToString(map);
        } catch (IOException e) {
            throw new ElasticsearchException(e.getMessage(), e);
        }

        for (String id : ids) {
            UpdateRequest updateRequest = new UpdateRequest(indexName, id);
            updateRequest.doc(doc, XContentType.JSON);
            updateRequest.docAsUpsert(false);
            updateRequest.routing(routing);

            UpdateQuery updateQuery = new UpdateQueryBuilder().withId(id).withIndexName(indexName).withDoUpsert(false)
                .withUpdateRequest(updateRequest).build();

            queries.add(updateQuery);
        }
        try {
            operations.bulkUpdate(queries);
        } catch (ElasticsearchException e) {
            if (e.getFailedDocuments() != null) {
                LOGGER.logError("Elasticsearch 批量更新失败, 失败数量: " + e.getFailedDocuments().size() + ", 总数量: " + ids.size(),
                    e);
                return ids.size() - e.getFailedDocuments().size();
            }
            throw e;
        }
        return ids.size();
    }

    /**
     * 条件删除（软删除）
     *
     * @param query
     * @param limit
     * @return
     */
    public long updateByQuery(@NotNull BoolQueryBuilder query, int limit) {
        String indexName = operations.getPersistentEntityFor(LogisticsOrderInfoSearchES.class).getIndexName() + "*";

        Map<String, Object> paramsMap = new HashMap<>(2);
        paramsMap.put(EsFields.isDeleted, true);
        paramsMap.put(EsFields.modified, minuteSecondFormatter.format(LocalDateTime.now()));
        StringBuilder sb = new StringBuilder();
        paramsMap.keySet().stream().forEach(p -> sb.append("ctx._source.").append(p).append("=params.").append(p).append(";"));

        UpdateByQueryRequest updateByQueryRequest = new UpdateByQueryRequest(indexName)
            .setMaxDocs(limit)
            .setQuery(query)
            .setAbortOnVersionConflict(false)
            .setScript(new Script(ScriptType.INLINE, "painless", sb.toString(), paramsMap));
        try {
            BulkByScrollResponse response = client.updateByQuery(updateByQueryRequest, RequestOptions.DEFAULT);
            if (response.isTimedOut()) {
                throw new ElasticsearchException(
                    "TimeOut while deleting item request: " + updateByQueryRequest.toString());
            }
            return response.getTotal();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while deleting item request: " + updateByQueryRequest.toString(), e);
        }
    }

    public T getById(@NotNull T entity, String... fields) {
        GetRequest request = new GetRequest(getIndexName(entity))
            .id(entity.getId()).routing(getRouting(entity));
        if (fields != null) {
            request.fetchSourceContext(new FetchSourceContext(true, fields, null));
        }
        try {
            GetResponse response = client.get(request, RequestOptions.DEFAULT);
            return (T) resultsMapper.mapResult(response, entity.getClass());
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while getting for request: " + request.toString(), e);
        }
    }

    /**
     * 软删除
     *
     * @param entity
     * @return
     */
    public int softDeleteById(@NotNull T entity) {
        UpdateRequest updateRequest = new UpdateRequest(getIndexName(entity), entity.getId());
        updateRequest.doc(ImmutableMap.of(
            EsFields.isDeleted, true,
            EsFields.gmtModified, formatter.format(LocalDateTime.now())
        ));
        updateRequest.docAsUpsert(false);
        updateRequest.routing(getRouting(entity));
        try {
            client.update(updateRequest, RequestOptions.DEFAULT);
        } catch (ElasticsearchStatusException e) {
            if (e.status() == RestStatus.NOT_FOUND) {
                return 0;
            }
            throw e;
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while deleting item request: " + updateRequest.toString(), e);
        }
        return 1;
    }

    /**
     * 真删除
     *
     * @param entity
     * @return
     */
    public int deleteById(@NotNull T entity) {
        DeleteRequest request = new DeleteRequest(getIndexName(entity))
            .id(entity.getId())
            .routing(getRouting(entity));
        try {
            DeleteResponse response = client.delete(request, RequestOptions.DEFAULT);
            if (response.getResult() == DocWriteResponse.Result.DELETED) {
                return 1;
            }
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while deleting item request: " + request.toString(), e);
        }
        return 0;
    }

    /**
     * 查询删除
     *
     * @param query
     * @param limit
     * @return 删除的条数 (包括删除失败的)
     */
    public long deleteByQuery(@NotNull String indexName, String routing, @NotNull QueryBuilder query, int limit) {
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(indexName);
        deleteByQueryRequest.setRouting(routing);
        deleteByQueryRequest.setMaxDocs(limit);
        deleteByQueryRequest.setAbortOnVersionConflict(false);
        deleteByQueryRequest.setQuery(query);
        try {
            BulkByScrollResponse response = client.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            if (response.isTimedOut()) {
                throw new ElasticsearchException(
                    "TimeOut while deleting item request: " + deleteByQueryRequest.toString());
            }
            return response.getTotal();
        } catch (IOException e) {
            throw new ElasticsearchException(
                "Error while deleting item request: " + deleteByQueryRequest.toString(), e);
        }
    }

    /**
     * 开启滚动窗口查询
     *
     * @param searchQuery
     * @param clazz
     * @return
     */
    public ScrolledPage<T> startScroll(SearchQuery searchQuery, Class<T> clazz) {
        return operations.startScroll(elasticsearchConfiguration.getScrollTimeInMillis(), searchQuery, clazz);
    }

    /**
     * 滚动窗口翻页
     *
     * @param scrollId
     * @param clazz
     * @return
     */
    public ScrolledPage<T> continueScroll(@Nullable String scrollId, Class<T> clazz) {
        return operations.continueScroll(scrollId, elasticsearchConfiguration.getScrollTimeInMillis(), clazz);
    }

    /**
     * 清除滚动窗口
     *
     * @param scrollId
     */
    public void clearScroll(String scrollId) {
        operations.clearScroll(scrollId);
    }

    @SneakyThrows
    protected SearchResultDTO<T> searchAfter(SearchRequest request, SearchSourceBuilder searchSourceBuilder, Class<T> resultClazz) {
        SearchResponse response = client.search(request, RequestOptions.DEFAULT);
        if (response.getHits() == null || response.getHits().getHits() == null) {
            throw new ElasticsearchException("elasticsearch响应异常，返回字段不包含hits");
        }
        SearchResultDTO<T> searchAfterResultDTO = new SearchResultDTO();

        SearchHit[] hits = response.getHits().getHits();
        if (hits.length == 0) {
            searchAfterResultDTO.setSearchResults(Lists.newArrayList());
        } else {
            Object[] sortValues = hits[hits.length - 1].getSortValues();
            searchAfterResultDTO.setSearchAfterSortValues(sortValues);

            searchSourceBuilder.searchAfter(sortValues);
            request.source(searchSourceBuilder);
            AggregatedPage<T> ayTradeSearchESPage = resultsMapper.mapResults(response, resultClazz, null);

            searchAfterResultDTO.setTotalResults((int) ayTradeSearchESPage.getTotalElements());
            searchAfterResultDTO.setSearchResults(ayTradeSearchESPage.getContent());
        }
        return searchAfterResultDTO;
    }

    /**
     * 创建SearchSourceBuilder
     *
     * @param queryCondition
     * @param includes
     * @param excludes
     * @param size
     * @param sort
     * @return
     */
    protected SearchSourceBuilder createSearchAfterSourceBuilder(BoolQueryBuilder queryCondition,
                                                                 String[] includes,
                                                                 String[] excludes,
                                                                 int size,
                                                                 SortBuilder<?>... sort) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder
            .query(filterQuery(queryCondition))
            .fetchSource(includes, excludes)
            .size(size);
        if (ArrayUtils.isEmpty(sort)) {
            searchSourceBuilder
                .sort(SortBuilders.fieldSort(_DOC).order(SortOrder.DESC))
                .sort(SortBuilders.fieldSort(EsFields.id).order(SortOrder.DESC));
        } else {
            for (SortBuilder<?> sortBuilders : sort) {
                searchSourceBuilder.sort(sortBuilders);
            }
        }
        return searchSourceBuilder;
    }

    public QueryBuilder filterQuery(QueryBuilder queryBuilder){
        if(elasticsearchConfiguration.isFilterEnable()){
            return boolQuery().filter(queryBuilder);
        }else{
            return queryBuilder;
        }
    }

}

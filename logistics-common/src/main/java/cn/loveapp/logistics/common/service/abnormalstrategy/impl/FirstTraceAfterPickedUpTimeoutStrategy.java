package cn.loveapp.logistics.common.service.abnormalstrategy.impl;


import cn.loveapp.common.utils.DateUtil;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.constant.AyLogisticsStatus;
import cn.loveapp.logistics.common.annotation.LogisticsAbnormalStrategyAnnotation;
import cn.loveapp.logistics.common.bo.LogisticsHandleBo;
import cn.loveapp.logistics.common.config.LogisticsAbnormalCheckConfig;
import cn.loveapp.logistics.api.constant.AbnormalProcessStatus;
import cn.loveapp.logistics.common.constant.EsFields;
import cn.loveapp.logistics.common.constant.LogisticsAbnormal;
import cn.loveapp.logistics.common.constant.UserAbnormalSettingConstant;
import cn.loveapp.logistics.common.dto.AbnormalQueryDTO;
import cn.loveapp.logistics.common.dto.LogisticsAbnormalCountDTO;
import cn.loveapp.logistics.common.entity.mongo.LogisticsOrderInfo;
import cn.loveapp.logistics.common.service.abnormalstrategy.ExecuteResult;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import static org.elasticsearch.index.query.QueryBuilders.*;
import static org.elasticsearch.index.query.QueryBuilders.existsQuery;

/**
 * 揽收后更新超时：定义为揽收后第一次物流未在用户设置时间内更新
 */
@Component
@LogisticsAbnormalStrategyAnnotation
public class FirstTraceAfterPickedUpTimeoutStrategy extends AbstractLogisticsAbnormalStrategy {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(FirstTraceAfterPickedUpTimeoutStrategy.class);

    @Autowired
    protected LogisticsAbnormalCheckConfig checkConfig;


    @Override
    public ExecuteResult execute(LogisticsOrderInfo logisticsOrderInfo, Map<String, String> userSettings) {
        String lastAction = logisticsOrderInfo.getLastAction();
        LocalDateTime lastActionModified = DateUtil.parseDate(logisticsOrderInfo.getLastActionModified());
        if (lastActionModified == null || StringUtils.isEmpty(lastAction)) {
            return new ExecuteResult(false, getAbnormalDeadline(null));
        }

        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LocalDateTime now = LocalDateTime.now();
        boolean isUpdate = false;

        boolean enable = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_TRANSFER_TIMEOUT_ENABLE, true, Boolean.class);
        if (!enable) {
            return new ExecuteResult();
        }

        Integer checkTime = getSetting(userSettings, UserAbnormalSettingConstant.LOGISTICS_FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT, 12, Integer.class);

        boolean lastActionIsPickUp = AyLogisticsStatus.PICKED_UP.value().equals(lastAction);
        // 揽收后N小时未更新第一次物流轨迹 上次物流更新时间 + N小时 < 当前时间 && 揽收后没有第一次物流更新
        boolean checkFirstTraceAfterPickTimeout = lastActionIsPickUp && lastActionModified.plusHours(checkTime).isBefore(now);;

        LogisticsOrderInfo.AbnormalDetails firstTraceAfterPickedUpTimeout = logisticsAbnormalInfo.getFirstTraceAfterPickedUpTimeout();
        if (Objects.isNull(firstTraceAfterPickedUpTimeout)) {
            firstTraceAfterPickedUpTimeout = new LogisticsOrderInfo.AbnormalDetails();
        }
        isUpdate = LogisticsOrderInfo.generalAbnormalDetails(checkFirstTraceAfterPickTimeout,
            firstTraceAfterPickedUpTimeout, lastActionModified.plusHours(checkTime));
        if (checkFirstTraceAfterPickTimeout || isUpdate) {
            LOGGER.logInfo(logisticsOrderInfo.getOutSid() + "执行策略：【揽收后" + checkTime + "小时后未更新第一次物流】，判断结果："
                + checkFirstTraceAfterPickTimeout + ", 是否变更：" + isUpdate);
        }

        if (isUpdate) {
            logisticsAbnormalInfo.setFirstTraceAfterPickedUpTimeout(firstTraceAfterPickedUpTimeout);
        }

        logisticsOrderInfo.checkAndSetMainAbnormalInfo(checkFirstTraceAfterPickTimeout, isUpdate);
        if (isUpdate && checkFirstTraceAfterPickTimeout) {
            appendLogisticsAbnormalTypeHistory(logisticsOrderInfo, getAbnormalType());
        }

        return new ExecuteResult(isUpdate, getAbnormalDeadline(lastActionModified));
    }


    @Override
    public String getAbnormalType() {
        return LogisticsAbnormal.FIRST_TRACE_AFTER_PICKED_UP_TIMEOUT.value();
    }

    @Override
    public void calculateAndSetAbnormalCount(LogisticsAbnormalCountDTO abnormalCountDTO, Integer abnormalCount) {
        Integer countAll = abnormalCountDTO.getFirstTraceAfterPickedUpTimeoutCount();
        if (countAll == null) {
            countAll = 0;
        }

        if (!Objects.isNull(abnormalCount)) {
            countAll += abnormalCount;
        }
        abnormalCountDTO.setFirstTraceAfterPickedUpTimeoutCount(countAll);
    }

    @Override
    public BoolQueryBuilder generalAbnormalBoolQuery(AbnormalQueryDTO abnormalQueryDTO) {
        BoolQueryBuilder queryCondition = boolQuery();
        queryCondition.must(termQuery(EsFields.firstTraceAfterPickedUpTimeoutIsExists, true))
            .must(existsQuery(EsFields.consignTime))
            .mustNot(termQuery(EsFields.otherAbnormalOfTradeAppIsExists, true));

        appendQueryBuilder(abnormalQueryDTO, queryCondition);

        if (abnormalQueryDTO == null) {
            return queryCondition;
        }
        AbnormalProcessStatus processStatus = abnormalQueryDTO.getProcessStatus();
        if (AbnormalProcessStatus.PENDING.equals(processStatus)) {
            queryCondition.must(boolQuery()
                .should(termsQuery(EsFields.firstTraceAfterPickedUpTimeoutProcessStatus, AbnormalProcessStatus.PENDING))
                .should(boolQuery().mustNot(existsQuery(EsFields.firstTraceAfterPickedUpTimeoutProcessStatus))));
        }

        return queryCondition;
    }

    @Override
    public boolean setLogisticsProcessStatus(LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo,
        String newProcessStatus, String appName) {
        LogisticsOrderInfo.AbnormalDetails firstTraceAfterPickedUpTimeout =
            logisticsAbnormalInfo.getFirstTraceAfterPickedUpTimeout();
        if (Objects.isNull(firstTraceAfterPickedUpTimeout)) {
            return false;
        }
        // 判断是否能更新
        if (LogisticsHandleBo.checkProcessStatusUpdate(firstTraceAfterPickedUpTimeout.getProcessStatus(),
            newProcessStatus, appName)) {
            firstTraceAfterPickedUpTimeout.setProcessStatus(newProcessStatus);
            logisticsAbnormalInfo.setFirstTraceAfterPickedUpTimeout(firstTraceAfterPickedUpTimeout);
            return true;
        }
        return false;
    }

    @Override
    public boolean checkHasAbnormal(LogisticsOrderInfo logisticsOrderInfo) {
        if (logisticsOrderInfo == null || !BooleanUtils.isTrue(logisticsOrderInfo.getHasAbnormal()) || logisticsOrderInfo.getLogisticsAbnormalInfo() == null) {
            return false;
        }
        LogisticsOrderInfo.LogisticsAbnormalInfo logisticsAbnormalInfo = logisticsOrderInfo.getLogisticsAbnormalInfo();
        LogisticsOrderInfo.AbnormalDetails abnormalDetails = logisticsAbnormalInfo.getFirstTraceAfterPickedUpTimeout();
        return !Objects.isNull(abnormalDetails) && BooleanUtils.isTrue(abnormalDetails.getIsExists()) && !AbnormalProcessStatus.PROCESSED.value().equals(abnormalDetails.getProcessStatus());
    }

    @Override
    public boolean checkNeedSendAbnormalNotify(Map<String, String> userSettings) {
        return false;
    }

    @Override
    LocalDateTime getAbnormalDeadline(LocalDateTime lastActionModified) {
        int checkTimeMin = checkConfig.getFirstTraceAfterPickedUpTimeoutMaxCheckTime();
        int checkTimeMax = checkConfig.getFirstTraceAfterPickedUpTimeoutMinCheckTime();
        return getAbnormalDeadline(true, checkTimeMax, checkTimeMin, lastActionModified);
    }
}

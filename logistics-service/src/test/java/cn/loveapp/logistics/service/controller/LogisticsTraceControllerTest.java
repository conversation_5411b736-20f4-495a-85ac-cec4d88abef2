package cn.loveapp.logistics.service.controller;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.http.Cookie;

import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.dto.LogisticsDetailDTO;
import cn.loveapp.logistics.service.exception.DatabaseException;
import cn.loveapp.logistics.service.exception.ExceptionEnum;
import cn.loveapp.logistics.service.service.LogisticsTraceService;
import cn.loveapp.logistics.service.utils.MemcacheUtil;

@Ignore
@RunWith(SpringRunner.class)
@WebMvcTest({LogisticsTraceController.class})
public class LogisticsTraceControllerTest {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsTraceController.class);

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private LogisticsTraceService logisticsTraceService;

    @MockBean
    private MemcacheUtil memcacheUtil;

    private final String memcacheValue =
        "table_id|s:3:\"0\";" + "taobao_user_nick|s:9:\"taobao_user_nick\";" + "taobao_user_id|s:8:\"taobao_user_id\"";
    private final String memcacheValueNoNick = "taobao_user_id|s:8:\"taobao_user_id\"";

    @Test
    public void getLogisticInfo() throws Exception {
        final String oid = "232208741955573679,234361125325131069,234277604515656989";
        List<LogisticsDetailDTO> logisticsDetailDTO = new ArrayList<>();

        MultiValueMap<String, String> testParam = new LinkedMultiValueMap<>();
        testParam.add("oid", "");
        String cookieValue = "";
        for (int i = 0; i < 4; i++) {

            if (1 == i) {
                testParam.add("oid", oid);
            }
            if (2 == i) {
                when(memcacheUtil.getCache("111")).thenReturn(memcacheValueNoNick);
                cookieValue = "111";
            }
            if (3 == i) {
                when(memcacheUtil.getCache("111")).thenReturn(memcacheValue);
                cookieValue = "111";
            }
            MvcResult mvcResult = mockMvc
                .perform(get("/logistics/logisticsInfo").params(testParam).cookie(new Cookie("PHPSESSID", cookieValue)))
                .andExpect(status().isOk()).andReturn();
            String content = mvcResult.getResponse().getContentAsString();
            if (0 == i) {
                Assert.assertEquals(ExceptionEnum.TID_IS_NULL.message, content);
            }

            if (1 == i) {
                Assert.assertEquals(ExceptionEnum.SESSIONID_ERROR.message, content);
            }
            if (2 == i) {
                Assert.assertEquals(ExceptionEnum.SESSION_INVALIDATION.message, content);
            }
            if (3 == i) {
                Assert.assertEquals(JSON.toJSONString(logisticsDetailDTO), content);
            }
            LOGGER.logDebug("【getLogisticInfo共4次测试】第" + i + "次测试成功");
        }
    }

    @Test
    public void reloadLogistics() throws Exception {

        MultiValueMap<String, String> testParam = new LinkedMultiValueMap<>();

        String cookieValue = "";
        for (int i = 0; i < 5; i++) {

            if (1 == i) {
                testParam.add("logisticsData[0].company_name", "中通快运");
                testParam.add("logisticsData[0].out_sid", "201625518885");
                testParam.add("logisticsData[0].status", "对方已签收");
                testParam.add("logisticsData[0].tid", "256198853518806287");
                testParam.add("logisticsData[0].trace_list.transit_step_info[0].action", "CONSIGN");
                testParam.add("logisticsData[0].trace_list.transit_step_info[0].status_desc", "包裹正在等待揽收");
                testParam.add("logisticsData[0].trace_list.transit_step_info[0].status_time", "2018-12-20 12:56:40");
                testParam.add("logisticsData[0].trace_list.transit_step_info[1].action", "TMS_ACCEPT");
                testParam.add("logisticsData[0].trace_list.transit_step_info[1].status_desc", "您的包裹已由物流公司揽收");
                testParam.add("logisticsData[0].trace_list.transit_step_info[1].status_time", "2018-12-20 17:23:42");
                testParam.add("logisticsData[0].trace_list.transit_step_info[2].action", "TMS_STATION_OUT");
                testParam.add("logisticsData[0].trace_list.transit_step_info[2].status_desc", "嵊州发件,下一网点杭州分拨中心");
                testParam.add("logisticsData[0].trace_list.transit_step_info[2].status_time", "2018-12-20 17:30:53");
                testParam.add("logisticsData[0].request_id", "3k53auauqs6n");
                testParam.add("logisticsData[0].sub_tids.number[0]", "256198853518806287");
                testParam.add("logisticsData[0].query.tid]", "256198853518806287");
                testParam.add("logisticsData[0].query.seller_nick", "aierfuda1987");
                testParam.add("logisticsData[0].is_split", "undefined");
                testParam.add("logisticsData[0].seller_nick", "aierfuda1987");
                testParam.add("logisticsData[0].consign_time", "2018-12-20 12:56:41");
                testParam.add("trade_source", "TAO");
            }
            if (2 == i) {
                when(memcacheUtil.getCache("111")).thenReturn(memcacheValueNoNick);
                cookieValue = "111";
            }
            if (3 == i) {
                when(memcacheUtil.getCache("111")).thenReturn(memcacheValue);
                cookieValue = "111";
            }
            if (4 == i) {
                doThrow(new DatabaseException(ExceptionEnum.UPDATE_DATA_IS_EMPTY)).when(logisticsTraceService)
                    .updateLogisticsInfo(any(), any());
            }

            MvcResult mvcResult = null;
            try {
                mvcResult = mockMvc.perform(post("/logistics/updateLogisticsPgsql").params(testParam)
                    .cookie(new Cookie("PHPSESSID", cookieValue))).andExpect(status().isOk()).andReturn();
            } catch (DatabaseException e) {
                // i == 4
                Assert.assertTrue(true);
            }
            String content = mvcResult.getResponse().getContentAsString();
            if (0 == i) {
                Assert.assertEquals("缺少必要参数", content);
            }
            if (1 == i) {
                Assert.assertEquals(ExceptionEnum.SESSIONID_ERROR.message, content);
            }
            if (2 == i) {
                Assert.assertEquals(ExceptionEnum.SESSION_INVALIDATION.message, content);
            }
            if (3 == i) {
                // 成功
                Assert.assertEquals("{}", content);
            }

            LOGGER.logDebug("【getLogisticInfo共4次测试】第" + i + "次测试成功");
        }
    }

}

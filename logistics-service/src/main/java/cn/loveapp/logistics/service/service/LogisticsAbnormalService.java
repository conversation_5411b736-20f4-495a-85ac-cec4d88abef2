package cn.loveapp.logistics.service.service;

import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.exception.LogisticsHandlesException;
import cn.loveapp.logistics.service.dto.AbnormalProcessInfo;
import cn.loveapp.logistics.service.dto.AbnormalProcessResult;
import cn.loveapp.logistics.service.request.LogisticsAbnormalListGetRequest;
import cn.loveapp.logistics.service.response.LogisticsAbnormalListGetResponse;

import java.util.List;

/**
 * 异常物流相关接口
 *
 * <AUTHOR>
 * @Date 2023/7/3 14:29
 */
public interface LogisticsAbnormalService {

    /**
     * 获取异常物流单列表
     *
     * @param request
     * @param userInfoDTO
     * @return
     */
    LogisticsAbnormalListGetResponse abnormalListGet(LogisticsAbnormalListGetRequest request, UserInfoDTO userInfoDTO) throws LogisticsHandlesException;

    /**
     * 异常物流单处理
     * @param abnormalProcessInfoList
     * @return
     * @throws LogisticsHandlesException
     */
    List<AbnormalProcessResult> abnormalBatchProcess(List<AbnormalProcessInfo> abnormalProcessInfoList);

    /**
     * 停止指定运单号的异常物流监控
     * @param abnormalProcessInfoList
     * @return
     */
    List<AbnormalProcessResult> abnormalStop(List<AbnormalProcessInfo> abnormalProcessInfoList);
}

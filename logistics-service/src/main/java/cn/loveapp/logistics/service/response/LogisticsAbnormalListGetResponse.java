package cn.loveapp.logistics.service.response;

import cn.loveapp.logistics.common.dto.LogisticsPackInfo;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 异常物流response
 *
 * <AUTHOR>
 * @Date 2023/7/1 18:14
 */
@Data
@ApiModel
public class LogisticsAbnormalListGetResponse {

    @ApiModelProperty(value = "包裹列表")
    @JSONField(name = "pack_list")
    @JsonProperty("pack_list")
    private List<LogisticsPackInfo> packList;

    @ApiModelProperty(value = "是否存在下一页")
    @JSONField(name = "has_next")
    @JsonProperty("has_next")
    private Boolean hasNext;

    @ApiModelProperty(value = "总数")
    @JSONField(name = "total_results")
    @JsonProperty("total_results")
    private Integer totalResults;

    /**
     * search after查询时，响应回去的游标值
     */
    @ApiModelProperty(value = "search after查询时，响应回去的游标值")
    @JsonProperty("search_sort_values")
    @JSONField(name="search_sort_values")
    private Object[] searchSortValues;
}

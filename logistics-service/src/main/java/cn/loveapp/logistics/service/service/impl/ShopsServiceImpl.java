package cn.loveapp.logistics.service.service.impl;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.web.CommonApiResponse;
import cn.loveapp.common.web.CommonApiStatus;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.service.service.ShopsService;
import cn.loveapp.shops.api.request.ShopsAuthRequest;
import cn.loveapp.shops.api.service.ShopsRpcInnerApiService;
import com.alibaba.fastjson.JSON;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 多店服务实现类
 *
 * <AUTHOR>
 */
@Service
public class ShopsServiceImpl implements ShopsService {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(ShopsServiceImpl.class);

    @Autowired
    private ShopsRpcInnerApiService shopsRpcInnerApiService;

    /**
     * 多店鉴权
     *
     * @param sellerNick
     * @param storeId
     * @param appName
     * @param targetNick
     * @param targetStoreId
     * @param targetAppName
     * @return
     */
    @Override
    public boolean shopsAuth(String sellerNick, String storeId, String appName, String targetNick, String targetStoreId, String targetAppName) {
        ShopsAuthRequest request = new ShopsAuthRequest();
        request.setSellerNick(sellerNick);
        request.setStoreId(storeId);
        request.setAppName(appName);
        request.setTargetNick(targetNick);
        request.setTargetStoreId(targetStoreId);
        request.setTargetAppName(targetAppName);
        return shopsAuthExcute(request);
    }


    private boolean shopsAuthExcute(ShopsAuthRequest request) {
        long start = System.currentTimeMillis();
        try {
            LOGGER.logInfo("调shops多店鉴权, request=" + JSON.toJSONString(request));
            CommonApiResponse response = shopsRpcInnerApiService.shopsAuth(request);
            LOGGER.logInfo("调shops多店鉴权, response=" + JSON.toJSONString(response));
            if (response != null && response.getCode().equals(CommonApiStatus.Success.code()) && null == response.getSubCode()) {
                return true;
            }
        } catch (Exception e) {
            LOGGER.logError("网络异常，多店鉴权失败", e);
        } finally {
            long time = System.currentTimeMillis() - start;
            LOGGER.logInfo("shops多店鉴权 请求耗时 " + time);
        }
        return false;
    }

    @Override
    public boolean multiShopsAuth(String sellerNick, String storeId, String appName, List<TargetSellerInfo> targetSellerList) {
        if (CollectionUtils.isEmpty(targetSellerList)) {
            LOGGER.logInfo(sellerNick, "", "需要鉴权的店铺集合为空");
            return true;
        }
        List<ShopsAuthRequest.TargetSeller> collect = targetSellerList.stream()
            .map(m -> new ShopsAuthRequest.TargetSeller(m.getTargetStoreId(), m.getTargetNick(), m.getTargetAppName()))
            .collect(Collectors.toList());
        ShopsAuthRequest request = new ShopsAuthRequest();
        request.setSellerNick(sellerNick);
        request.setStoreId(storeId);
        request.setAppName(appName);
        request.setTargetSellerList(collect);
        return shopsAuthExcute(request);
    }
}

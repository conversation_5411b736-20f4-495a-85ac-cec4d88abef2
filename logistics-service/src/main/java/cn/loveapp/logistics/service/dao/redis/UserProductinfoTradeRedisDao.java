package cn.loveapp.logistics.service.dao.redis;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.concurrent.TimeUnit;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import cn.loveapp.common.utils.LoggerHelper;

/**
 * <AUTHOR>
 * @date 2020/2/11
 */
@Repository
public class UserProductinfoTradeRedisDao {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserProductinfoTradeRedisDao.class);
    private static final String USER_ID_KEY = "userProductinfoTrade:userId:";

    /**
     * 设置 缓存时间（秒）
     */
    @Value("${userProductinfoTradeExt.redis.timeout: 86400}")
    private long redisTimeout;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    private String initUserKey(String nick) {
        try {
            return USER_ID_KEY + URLEncoder.encode(nick, "utf-8");
        } catch (UnsupportedEncodingException e) {
        }
        return USER_ID_KEY + nick;
    }

    public String getUserIdByNick(@NotNull String nick) {
        try {
            String key = initUserKey(nick);
            String value = stringRedisTemplate.opsForValue().get(key);
            if (StringUtils.isNotEmpty(value)) {
                return value;
            }
        } catch (Exception e) {
            LOGGER.logError(nick, "", "从redis获取用户UserProductinfoTradeExt信息失败: " + e.getMessage(), e);
        }
        return null;
    }

    public void flushUserId(@NotNull String nick, @NotNull String userId) {
        try {
            String key = initUserKey(nick);
            stringRedisTemplate.opsForValue().set(key, userId, redisTimeout, TimeUnit.SECONDS);
        } catch (Exception e) {
            LOGGER.logError(nick, "-", "刷新redis用户UserProductinfoTradeExt信息失败: " + e.getMessage(), e);
        }
    }
}

package cn.loveapp.logistics.service.request;

import cn.loveapp.logistics.common.constant.LogisticsPackType;
import cn.loveapp.logistics.common.constant.AySearchTypeEnum;
import cn.loveapp.logistics.common.constant.BuyerSellerMemoSearchType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 异常物流单列表查询request
 *
 * <AUTHOR>
 * @Date 2023/7/1 18:00
 */
@Data
@ApiModel
public class LogisticsAbnormalListGetRequest {

    private static final Integer MAX_PAGE_SIZE = 200;

    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 异常标识
     */
    @ApiModelProperty(name = "异常标识")
    private List<String> abnormalTypes;

    /**
     * 是否只查询存在异常的运单
     */
    @ApiModelProperty(name = "是否只查询存在异常的运单")
    private Boolean onlySearchAbnormal;

    /**
     * 运单号
     */
    @ApiModelProperty(name = "运单号")
    private String outSid;

    /**
     * 运单号列表
     */
    @ApiModelProperty(name = "运单号列表")
    private List<String> outSidList;

    /**
     * 包裹类型
     */
    @ApiModelProperty(name = "包裹类型")
    private LogisticsPackType packType;

    /**
     * 物流公司Code/name
     */
    @ApiModelProperty(name = "物流公司Code/name")
    private String logisticsCompany;

    /**
     * 快递公司集合
     */
    @ApiModelProperty(name = "物流公司Code/name")
    private List<String> logisticsCompanyList;

    /**
     * 开始时间
     */
    @ApiModelProperty(name = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(name = "结束时间")
    private LocalDateTime endTime;


    /**
     * 发货时间筛选开始时间
     */
    @ApiModelProperty(name = "发货时间筛选开始时间")
    private LocalDateTime startConsignTime;

    /**
     * 发货时间筛选结束时间
     */
    @ApiModelProperty(name = "发货时间筛选结束时间")
    private LocalDateTime endConsignTime;

    /**
     * 物流最新状态
     */
    @ApiModelProperty(name = "物流最新状态名称")
    private String lastAction;

    /**
     * 订单号
     */
    @ApiModelProperty(name = "订单号")
    private String tid;

    /**
     * 订单号或买家昵称
     */
    @ApiModelProperty(name = "订单号或买家昵称")
    private List<String> tidOrBuyerOpenUid;

    /**
     * 处理进度
     */
    @ApiModelProperty(name = "处理进度")
    private List<String> processStatus;

    /**
     * 是否需要查询轨迹列表
     */
    @ApiModelProperty(name = "是否需要查询轨迹列表")
    private Boolean isSearchTrace;

    /**
     * 是否排除已处理异常物流
     */
    @ApiModelProperty(name = "是否排除已处理异常物流")
    private Boolean isExcludeProcessed;

    @ApiModelProperty(name = "页码")
    private Integer pageNo;

    @ApiModelProperty(name = "页数")
    private Integer pageSize;

    /**
     * 物流单排序字段
     */
    @ApiModelProperty(name = "物流单排序字段")
    private String sortField;

    /**
     * 物流单排序方向
     */
    @ApiModelProperty(name = "物流单排序方向 asc/desc")
    private String sortDirection;

    /**
     * 轨迹排序方向 asc/desc
     */
    @ApiModelProperty(name = "轨迹排序方向 asc/desc")
    private String traceSortDirection;

    /**
     * 去重是否需要依据status字段
     */
    @ApiModelProperty(name = "去重是否需要依据status字段")
    private boolean distinctWithStatusField = true;

    /**
     * 是否只查询正常件运单
     */
    @ApiModelProperty(name = "是否只查询正常件运单")
    private Boolean onlySearchNormal;

    /**
     * 是否返回已处理状态的异常列表
     */
    @ApiModelProperty(name = "是否返回已处理状态的异常列表")
    private Boolean includeProcessedAbnormal;

    /**
     * 物流单是否必须存在发货时间
     */
    @ApiModelProperty(name = "物流单是否必须存在发货时间")
    private Boolean isExistConsignTime;

    /**
     * 是否排除交易其他异常
     */
    @ApiModelProperty(name = "是否排除交易其他异常")
    private Boolean isExcludeOtherAbnormalOfTradeApp;

    /**
     * 买家openUid
     */
    @ApiModelProperty(name = "买家openUid")
    private String buyerOpenUid;

    /**
     * search after查询时，响应回去的游标值
     */
    @ApiModelProperty(name = "search after查询时，响应回去的游标值")
    private Object[] lastSearchSortValues;

    /**
     * 订单旗帜
     */
    @ApiModelProperty(name = "订单旗帜")
    private List<Integer> orderSellerFlagList;

    /**
     * 订单自定义备注标记 对应订单的ayCustomFlag
     */
    @ApiModelProperty(name = "订单自定义旗帜")
    private List<Integer> orderAyCustomFlagList;

    /**
     * 买家留言卖家备注搜索类型
     */
    @ApiModelProperty(name = "买家留言卖家备注搜索类型")
    private BuyerSellerMemoSearchType buyerSellerMemoSearchType;

    /**
     * 是否存在退款
     */
    @ApiModelProperty(name = "是否存在退款")
    private Boolean isRefund;

    /**
     * sku名称
     */
    @ApiModelProperty(name = "sku名称")
    private String skuName;

    /**
     * sku外部编码
     */
    @ApiModelProperty(name = "sku外部编码")
    private String outerSkuId;

    /**
     * 旗帜搜索类型
     */
    @ApiModelProperty(name = "旗帜搜索类型")
    private AySearchTypeEnum orderFlagSearchType;

    /**
     * sku信息搜索类型
     */
    @ApiModelProperty(name = "sku信息搜索类型")
    private AySearchTypeEnum skuInfoSearchType;

    public LogisticsAbnormalListGetRequest() {
        this.pageNo = 1;
        this.pageSize = 20;
        this.isSearchTrace = true;
        this.onlySearchAbnormal = true;
        this.isExcludeProcessed = true;
    }

    public void setStartTime(String startTime) {
        if (!StringUtils.isEmpty(startTime)) {
            this.startTime = LocalDateTime.parse(startTime, DF);
        }
    }

    public void setEndTime(String endTime) {
        if (!StringUtils.isEmpty(endTime)) {
            this.endTime = LocalDateTime.parse(endTime, DF);
        }
    }

    public void setStartConsignTime(String startConsignTime) {
        if (!StringUtils.isEmpty(startConsignTime)) {
            this.startConsignTime = LocalDateTime.parse(startConsignTime, DF);
        }
    }
    public void setEndConsignTime(String endConsignTime) {
        if (!StringUtils.isEmpty(endConsignTime)) {
            this.endConsignTime = LocalDateTime.parse(endConsignTime, DF);
        }
    }

    public void setPageSize(Integer pageSize) {
        //最大值200，超过默认200条数
        if(MAX_PAGE_SIZE < pageSize ){
            pageSize = MAX_PAGE_SIZE;
        }
        this.pageSize = pageSize;
    }

    public boolean checkParams() {
        if (this.getPageNo() <= 0L || this.getPageSize() < 0L) {
            // pageNo和pageSize必须为正数
            return false;
        }
        return true;
    }
}

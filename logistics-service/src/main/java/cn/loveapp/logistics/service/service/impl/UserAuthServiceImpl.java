package cn.loveapp.logistics.service.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletRequest;

import cn.loveapp.common.dto.UserSessionInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSON;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.user.session.constant.SessionConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.dto.UserInfoDTO;
import cn.loveapp.logistics.common.entity.TargetSellerInfo;
import cn.loveapp.logistics.common.service.UserInfoService;
import cn.loveapp.logistics.service.exception.ShopsAuthException;
import cn.loveapp.logistics.service.service.ShopsService;
import cn.loveapp.logistics.service.service.UserAuthService;
import cn.loveapp.shops.api.request.ShopsAuthRequest;
import cn.loveapp.uac.request.UserInfoRequest;
import cn.loveapp.uac.response.UserInfoResponse;

@Component
public class UserAuthServiceImpl implements UserAuthService {

    public static final String TARGET_NICK = "targetNick";
    public static final String TARGET_APP_NAME = "targetAppName";
    public static final String TARGET_STORE_ID = "targetStoreId";

    /**
     * 多店铺传递参数 nick集合 nick1,nick2,nick3 英文,拼接 下标一一对应，不可和targetNick单店铺参数同时出现
     */
    public static final String TARGET_NICK_LIST = "targetNickList";

    /**
     * 多店铺传递参数 storeId集合 下标一一对应
     */
    public static final String TARGET_STORE_ID_LIST = "targetStoreIdList";

    /**
     * 多店铺传递参数 appName集合 下标一一对应
     */
    public static final String TARGET_APP_NAME_LIST = "targetAppNameList";

    /**
     * seller信息 分割符
     */
    private static final String SELLER_INFO_SPLIC_STR = ",";


    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(UserAuthServiceImpl.class);

    @Autowired
    private ShopsService shopsService;

    @Autowired
    private UserInfoService userInfoService;

    @Override
    public UserInfoDTO auth(HttpServletRequest request) throws ShopsAuthException {
        UserSessionInfo userSessionInfo =
            (UserSessionInfo)request.getAttribute(SessionConstants.REQUEST_ATTRIBUTE_SESSIONINFO);
        if (userSessionInfo == null) {
            throw new ShopsAuthException("缺失session信息");
        }

        String targetNick = request.getParameter(TARGET_NICK);
        String targetNickListStr = request.getParameter(TARGET_NICK_LIST);

        if (StringUtils.isNotEmpty(targetNick)) {
            return multiShopsAuthSingle(userSessionInfo, request);
        } else if (StringUtils.isNotEmpty(targetNickListStr)) {
            return multiShopsAuthBatch(userSessionInfo, request);
        } else {
            // 没有传入目标用户信息，不需要鉴权
            UserInfoDTO userInfoDTO = new UserInfoDTO();
            userInfoDTO.setSellerId(userSessionInfo.getSellerId());
            userInfoDTO.setNick(userSessionInfo.getNick());
            userInfoDTO.setAppName(userSessionInfo.getAppName());
            userInfoDTO.setStoreId(userSessionInfo.getStoreId());
            userInfoDTO.setVipFlag(userSessionInfo.getVipflag());
            return userInfoDTO;
        }
    }

    /**
     * 多店鉴权-单用户
     * @param userSessionInfo
     * @param request
     * @return
     * @throws ShopsAuthException
     */
    private UserInfoDTO multiShopsAuthSingle(UserSessionInfo userSessionInfo, HttpServletRequest request) throws ShopsAuthException {

        String targetNick = request.getParameter(TARGET_NICK);
        String targetStoreId = request.getParameter(TARGET_STORE_ID);
        String targetAppName = StringUtils.defaultIfEmpty(request.getParameter(TARGET_APP_NAME), CommonAppConstants.APP_TRADE);

        ShopsAuthRequest shopsAuthRequest = new ShopsAuthRequest();
        shopsAuthRequest.setSellerNick(userSessionInfo.getNick());
        shopsAuthRequest.setStoreId(userSessionInfo.getStoreId());
        shopsAuthRequest.setTargetNick(targetNick);
        shopsAuthRequest.setTargetStoreId(targetStoreId);

        boolean checkoutResult = shopsService.shopsAuth(userSessionInfo.getNick(), userSessionInfo.getStoreId(), userSessionInfo.getAppName(), targetNick, targetStoreId, targetAppName);
        if (!checkoutResult) {
            throw new ShopsAuthException("多店鉴权失败");
        }

        UserInfoRequest userInfoRequest = new UserInfoRequest();
        userInfoRequest.setApp(targetAppName);
        userInfoRequest.setPlatformId(targetStoreId);
        userInfoRequest.setSellerNick(targetNick);
        UserInfoResponse userInfoResponse = userInfoService.getSellerInfo(userInfoRequest);
        if(userInfoResponse == null){
            LOGGER.logWarn(targetNick, null, "访问的target用户不存在, targetNick=" + targetNick + "  targetAppName=" + targetAppName + " targetStoreId=" + targetStoreId);
            throw new ShopsAuthException("多店鉴权失败,访问的target用户不存在");
        }
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setSellerId(userInfoResponse.getSellerId());
        userInfoDTO.setNick(targetNick);
        userInfoDTO.setAppName(targetAppName);
        userInfoDTO.setStoreId(targetStoreId);
        userInfoDTO.setVipFlag(userInfoResponse.getVipflag());
        return userInfoDTO;

    }

    /**
     * 多店鉴权-多用户
     * @param userSessionInfo
     * @param request
     * @return
     * @throws ShopsAuthException
     */
    private UserInfoDTO multiShopsAuthBatch(UserSessionInfo userSessionInfo, HttpServletRequest request) throws ShopsAuthException{
        String nick = userSessionInfo.getNick();

        String targetNickListStr = request.getParameter(TARGET_NICK_LIST);
        String targetStoreIdListStr = request.getParameter(TARGET_STORE_ID_LIST);
        String targetAppNameListStr = request.getParameter(TARGET_APP_NAME_LIST);

        if (StringUtils.isAnyEmpty(targetNickListStr,targetStoreIdListStr,targetAppNameListStr)) {
            LOGGER.logInfo(userSessionInfo.getNick(), "", "多店铺查询参数不正确");
            throw new ShopsAuthException("多店鉴权失败,多店铺查询参数不正确");
        }

        String[] appNameArr = targetAppNameListStr.split(SELLER_INFO_SPLIC_STR);
        String[] storeIdArr = targetStoreIdListStr.split(SELLER_INFO_SPLIC_STR);
        String[] nickArr = targetNickListStr.split(SELLER_INFO_SPLIC_STR);
        if (appNameArr.length != storeIdArr.length || appNameArr.length != nickArr.length) {
            LOGGER.logInfo(userSessionInfo.getNick(),"","多店铺查询参数数量不匹配");
            throw new ShopsAuthException("多店鉴权失败,多店铺查询参数数量不匹配");
        }
        //多店查询
        List<TargetSellerInfo> targetSellerList = new ArrayList<>();
        for (int i = 0; i < nickArr.length; i++) {
            TargetSellerInfo sellerInfo = new TargetSellerInfo(nickArr[i],storeIdArr[i],appNameArr[i]);
            targetSellerList.add(sellerInfo);
        }

        boolean checkoutResult = shopsService.multiShopsAuth(userSessionInfo.getNick(), userSessionInfo.getStoreId(), userSessionInfo.getAppName(), targetSellerList);
        if (!checkoutResult) {
            throw new ShopsAuthException("多店鉴权失败");
        }

        Optional<TargetSellerInfo> first = targetSellerList.stream().filter(f -> f.getTargetNick().equals(nick) && f.getTargetStoreId().equals(userSessionInfo.getStoreId())).findFirst();
        if (!first.isPresent()) {
            targetSellerList.add(new TargetSellerInfo(nick, userSessionInfo.getStoreId(), userSessionInfo.getAppName()));
        }
        List<UserInfoResponse> userInfoResponses = userInfoService.getUserInfo(targetSellerList);
        if (CollectionUtils.isEmpty(userInfoResponses) || userInfoResponses.size() != targetSellerList.size()) {
            LOGGER.logWarn(userSessionInfo.getNick(), null, "访问的target用户集合不存在或者结果缺少: " + JSON.toJSONString(userInfoResponses));
            throw new ShopsAuthException("多店鉴权失败,访问的target用户集合不存在或者结果缺少");
        }

        //key:nick,value:userinfo
        Map<String, UserInfoResponse> nickAndUserInfoMap = userInfoResponses.stream().collect(Collectors.toMap(k -> genUniqueSellerKey(k.getPlatformId(),k.getAppName(),k.getSellerNick()), Function.identity(), (v1, v2) -> v2));
        String currentSellerUniqueKey = genUniqueSellerKey(userSessionInfo.getStoreId(), userSessionInfo.getAppName(), nick);
        //找出登录seller信息填充
        if (!nickAndUserInfoMap.containsKey(currentSellerUniqueKey)) {
            LOGGER.logWarn(userSessionInfo.getNick(), null, "target店铺集合查询结果中不存在登录店铺信息: " + JSON.toJSONString(userInfoResponses));
            throw new ShopsAuthException("多店鉴权失败,target店铺集合查询结果中不存在登录店铺信息");
        }

        targetSellerList.stream().forEach(f->{
            String uniqueSellerKey = genUniqueSellerKey(f.getTargetStoreId(), f.getTargetAppName(), f.getTargetNick());
            UserInfoResponse userInfoResponse = nickAndUserInfoMap.get(uniqueSellerKey);
            f.setTargetSellerId(userInfoResponse.getSellerId());
            f.setTargetCorpId(userInfoResponse.getCorpId());
            f.setTargetVipFlag(userInfoResponse.getVipflag());
        });

        UserInfoDTO userInfoDTO = new UserInfoDTO();
        userInfoDTO.setSellerId(userSessionInfo.getSellerId());
        userInfoDTO.setNick(userSessionInfo.getNick());
        userInfoDTO.setAppName(userSessionInfo.getAppName());
        userInfoDTO.setStoreId(userSessionInfo.getStoreId());
        userInfoDTO.setVipFlag(userSessionInfo.getVipflag());

        // 原targetList不存在当前登录用户并且不是爱用账号,将当前登录用户移除targetList
        if (!first.isPresent() &&
            !(CommonAppConstants.APP_AIYONG.equals(userSessionInfo.getAppName()) && CommonPlatformConstants.PLATFORM_AIYONG.equals(userSessionInfo.getStoreId()))) {
            targetSellerList.removeIf(targetSellerInfo -> userSessionInfo.getNick().equals(targetSellerInfo.getTargetNick()));
        }
        userInfoDTO.setTargetSellerList(targetSellerList);

        return userInfoDTO;
    }

    /**
     * 生成唯一的店家key
     * @return
     */
    private String genUniqueSellerKey(String storeId,String appName,String sellerNick){
        //storeid为淘宝时，appName为空，但是uac查询出来为trad，为了赋值时能匹配上，生成匹配key时appname转为""
        if (CommonPlatformConstants.PLATFORM_TAO.equals(storeId)) {
            appName = "";
        }
        return StringUtils.join(storeId, appName, sellerNick);
    }

}

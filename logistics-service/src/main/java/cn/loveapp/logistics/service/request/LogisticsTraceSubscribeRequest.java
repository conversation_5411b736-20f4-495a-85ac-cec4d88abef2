package cn.loveapp.logistics.service.request;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.logistics.api.constant.BusinessType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 物流轨迹订阅请求request
 *
 * <AUTHOR>
 * @Date 2023/5/30 16:18
 */
@ApiModel
@Data
public class LogisticsTraceSubscribeRequest {

    private static final DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 物流平台
     */
    @ApiModelProperty(value = "物流平台", required = true)
    @NotNull
    private String logisticsStoreId;

    /**
     * 物流应用
     */
    @ApiModelProperty(value = "物流应用")
    private String logisticsAppName = CommonAppConstants.APP_LOGISTICS;

    /**
     * 业务类型：
     */
    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull
    private BusinessType businessType;

    /**
     * 业务类型id（根据业务类型区分）
     */
    @ApiModelProperty(value = "业务类型id（根据业务类型区分）", required = true)
    @NotEmpty
    private List<String> businessIds;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    private String outSid;

    /**
     * 物流公司Code
     */
    @ApiModelProperty(value = "物流公司Code", required = true)
    @NotNull
    private String logisticsCompanyCode;

    /**
     * 轨迹订阅是否入库
     */
    @ApiModelProperty(value = "轨迹订阅是否入库")
    private boolean needSave = true;

    /**
     * 订阅轨迹是否需要消耗物流包额度
     */
    @ApiModelProperty(value = "订阅轨迹是否需要消耗物流包额度")
    private Boolean isSubscribeNeedDeductionQuota;

    /**
     * 订单信息列表
     */
    @ApiModelProperty(value = "订单信息列表")
    private List<OrderInfo> orderInfoList;

    /**
     * 订单信息列表
     */
    @ApiModelProperty(value = "买家nick")
    private String buyerNick;

    /**
     * 订单信息列表
     */
    @ApiModelProperty(value = "买家id")
    private String buyerOpenUid;

    /**
     * 收件人/寄件人手机 顺丰必传
     */
    @ApiModelProperty(value = "收件人/寄件人手机 顺丰必传")
    private String phone;

    @Data
    public static class OrderInfo {
        /**
         * 订单号
         */
        private String tid;

        /**
         * 卖家标识
         */
        private Integer sellerFlag;

        /**
         * 订单自定义旗帜
         */
        private Integer orderAyCustomFlag;

        /**
         * 卖家备注
         */
        private String sellerMemo;

        /**
         * 卖家留言
         */
        private String buyerMessage;

        /**
         * 是否退款
         */
        private Boolean isRefund;

        /**
         * 退款创建时间
         */
        private LocalDateTime refundCreatedTime;

        /**
         * sku信息列表
         */
        private List<SkuInfo> skuInfoList;


        public void setRefundCreatedTime(String refundCreatedTime) {
            if(!StringUtils.isEmpty(refundCreatedTime)){
                this.refundCreatedTime = LocalDateTime.parse(refundCreatedTime,DF);
            }
        }
    }

    @Data
    public static class SkuInfo {
        /**
         * skuId
         */
        private String skuId;

        /**
         * 图片url
         */
        private String picUrl;

        /**
         * sku名称
         */
        private String skuName;

        /**
         * sku外部编码
         */
        private String outerSkuId;

        /**
         * 商品数量
         */
        private Integer num;
    }
}

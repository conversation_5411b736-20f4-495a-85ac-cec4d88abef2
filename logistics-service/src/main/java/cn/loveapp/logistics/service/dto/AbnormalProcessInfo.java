package cn.loveapp.logistics.service.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 物流变更信息传输对象
 *
 * <AUTHOR>
 * @Date 2023/7/5 11:18
 */
@Data
@ApiModel
public class AbnormalProcessInfo {

    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    private String outSid;

    @ApiModelProperty(value = "用户id", required = true)
    @NotNull
    private String sellerId;

    @ApiModelProperty(value = "平台", required = true)
    @NotNull
    private String storeId;

    @ApiModelProperty(value = "应用", required = true)
    @NotNull
    private String appName;

    @ApiModelProperty(value = "物流公司")
    private String companyCode;

    @ApiModelProperty(value = "处理状态")
    private String processStatus;

}

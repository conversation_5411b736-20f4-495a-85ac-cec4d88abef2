apiVersion: extensions/v1beta1
kind: Deployment
metadata:
    name: logistics-onsconsumer-deployment
spec:
    template:
        metadata:
            labels:
                app: logistics-onsconsumer
        spec:
            imagePullSecrets:
                -   name: registry-secret
            containers:
                -   image: registryzjk.aiyongtech.com/logistics-onsconsumer:1.0.0
                    name: logistics-onsconsumer
                    command: [ "sh","-c","java -Xmx6G -Djava.library.path=/usr/local/apr/lib:/usr/java/packages/lib/amd64:/usr/lib64:/lib64:/lib:/usr/lib -Dspring.profiles.active=prod -XX:+UseG1GC -jar /data/srv/logistics-onsconsumer/logistics-onsconsumer-1.0.0.jar" ]
                    resources:
                        requests:
                            cpu: "500m"
                            memory: "500Mi"
                        limits:
                            cpu: 4
                            memory: "8Gi"
                    volumeMounts:
                        -   mountPath: /dev/shm/tmp
                            name: cache-volume
            volumes:
                -   name: cache-volume
                    emptyDir: { }
---
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
    name: logistics-onsconsumer-hpa
    namespace: default
spec:
    scaleTargetRef:
        apiVersion: apps/v1
        kind: Deployment
        name: logistics-transfer-deployment
    minReplicas: 2
    maxReplicas: 4
    metrics:
        -   type: Resource
            resource:
                name: cpu
                target:
                    type: Utilization
                    averageUtilization: 600

package cn.loveapp.logistics.onsconsumer.service.impl;

import static org.mockito.BDDMockito.*;

import java.net.URLEncoder;

import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.boot.test.mock.mockito.SpyBean;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import cn.loveapp.logistics.onsconsumer.dao.dream.UserProductinfoTradeDao;
import cn.loveapp.logistics.onsconsumer.entity.UserProductinfoTrade;
import cn.loveapp.logistics.onsconsumer.service.TaobaoService;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE, classes = {TaobaoServiceImpl.class})
@ActiveProfiles("test")
public class TaobaoServiceImplTest {

    @MockBean
    private StringRedisTemplate redisHelper;

    @MockBean
    private UserProductinfoTradeDao userProductinfoTradeDao;

    @MockBean
    private HashOperations<String, Object, Object> operations;

    @SpyBean
    private TaobaoServiceImpl taobaoServiceImpl;

    private String nick = "nick";

    @Before
    public void setUp() throws Exception {
        taobaoServiceImpl.enableRedisRebuildUserUrl = true;
        doNothing().when(taobaoServiceImpl).http(anyString());
    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void getVipflag() throws Exception {
        // 1. 数据库返回是vip
        doReturn(true).when(taobaoServiceImpl).getVipflagFromDB(eq(nick));

        // 1.1 正常是vip
        doReturn("1").when(taobaoServiceImpl).getVipflagFromRedis(eq(nick));

        Assert.assertTrue(taobaoServiceImpl.getVipflag(nick));

        verify(taobaoServiceImpl, never()).getVipflagFromDB(anyString());
    }

    @Test
    public void getVipflag2() throws Exception {
        String nick = "nick";
        // 1. 数据库返回是vip
        doReturn(true).when(taobaoServiceImpl).getVipflagFromDB(eq(nick));

        // 1.2 正常不是vip
        doReturn("2").when(taobaoServiceImpl).getVipflagFromRedis(eq(nick));

        Assert.assertFalse(taobaoServiceImpl.getVipflag(nick));

        verify(taobaoServiceImpl, never()).getVipflagFromDB(anyString());
    }

    @Test
    public void getVipflag3() throws Exception {

        // 1. 数据库返回是vip
        doReturn(false).when(taobaoServiceImpl).getVipflagFromDB(eq(nick));
        doReturn("").when(taobaoServiceImpl).getVipflagFromRedis(eq(nick));

        taobaoServiceImpl.enableRedisRebuildUserUrl = false;

        Assert.assertFalse(taobaoServiceImpl.getVipflag(nick));

        verify(taobaoServiceImpl).getVipflagFromRedis(eq(nick));
        verify(taobaoServiceImpl).getVipflagFromDB(eq(nick));
    }

    @Test
    public void getVipflag4() throws Exception {
        String nick = "nick";
        // 1. 数据库返回是vip
        doReturn(false).when(taobaoServiceImpl).getVipflagFromDB(eq(nick));
        doReturn("").when(taobaoServiceImpl).getVipflagFromRedis(eq(nick));

        Assert.assertFalse(taobaoServiceImpl.getVipflag(nick));

        verify(taobaoServiceImpl, times(3)).getVipflagFromRedis(eq(nick));
        verify(taobaoServiceImpl).getVipflagFromDB(eq(nick));
    }

    @Test
    public void getVipflagFromRedis() throws Exception {
        given(redisHelper.opsForHash()).willReturn(operations);
        when(operations.get(URLEncoder.encode(nick, "utf-8"), taobaoServiceImpl.VIPFLAG)).thenReturn("true");
        Assert.assertEquals(taobaoServiceImpl.getVipflagFromRedis(nick), "true");
        verify(operations).get(anyString(), anyString());
    }

    @Test
    public void getVipflagFromRedis2() throws Exception {
        when(redisHelper.opsForHash()).thenReturn(operations);
        when(operations.get(URLEncoder.encode(nick, "utf-8"), taobaoServiceImpl.VIPFLAG)).thenReturn(null);
        Assert.assertNull(taobaoServiceImpl.getVipflagFromRedis(nick));
        verify(operations, times(2)).get(anyString(), anyString());
    }

    @Test
    public void getPartitionId() {
        Assert.assertEquals(1, taobaoServiceImpl.getPartitionId("100001"));
        Assert.assertEquals(11, taobaoServiceImpl.getPartitionId("100011"));
        Assert.assertEquals(111, taobaoServiceImpl.getPartitionId("111111"));
        Assert.assertEquals(2, taobaoServiceImpl.getPartitionId("2"));
    }

    @Test
    public void getUserIdFromDb() throws Exception {
        when(redisHelper.opsForHash()).thenReturn(operations);
        UserProductinfoTrade trade = new UserProductinfoTrade();
        trade.setUserId(11111L);
        when(userProductinfoTradeDao.queryUserIdAndVipflagByNick(eq(nick))).thenReturn(trade);
        Assert.assertEquals(trade.getUserId().toString(), taobaoServiceImpl.getUserId(nick, "tid"));

        when(userProductinfoTradeDao.queryUserIdAndVipflagByNick(eq(nick))).thenReturn(null);
        Assert.assertEquals(TaobaoService.EMPTY_USER_ID, taobaoServiceImpl.getUserId(nick, "tid"));

        when(operations.get(URLEncoder.encode(nick, "utf-8"), TaobaoServiceImpl.TAOBAO_USER_ID)).thenReturn("2222");

        Assert.assertEquals("2222", taobaoServiceImpl.getUserId(nick, "tid"));
    }

    @Test
    public void getVipflagFromDB() {
        UserProductinfoTrade trade = new UserProductinfoTrade();
        trade.setVipflag(1);
        when(userProductinfoTradeDao.queryUserIdAndVipflagByNick(eq(nick))).thenReturn(trade);
        Assert.assertEquals(true, taobaoServiceImpl.getVipflagFromDB(nick));
        trade.setVipflag(0);
        Assert.assertEquals(false, taobaoServiceImpl.getVipflagFromDB(nick));
        when(userProductinfoTradeDao.queryUserIdAndVipflagByNick(eq(nick))).thenReturn(null);
        Assert.assertEquals(false, taobaoServiceImpl.getVipflagFromDB(nick));
    }

}

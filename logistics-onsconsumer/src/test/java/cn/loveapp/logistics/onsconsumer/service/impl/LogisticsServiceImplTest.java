// package cn.loveapp.logistics.onsconsumer.service.impl;
//
// import cn.loveapp.logistics.onsconsumer.TestConfig;
// import cn.loveapp.logistics.onsconsumer.dao.dream.UserProductinfoTradeDao;
// import cn.loveapp.logistics.onsconsumer.dao.logistics.LogisticsErrorPackageDao;
// import cn.loveapp.logistics.onsconsumer.dao.logistics.LogisticsPackageListenDao;
// import cn.loveapp.logistics.onsconsumer.dao.logistics.LogisticsTranceInfoDao;
// import cn.loveapp.logistics.onsconsumer.dao.order.TradeListDao;
// import cn.loveapp.logistics.onsconsumer.dao.print.TradeLogisticsRuleDao;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsInfoDTO;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsStatus;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsErrorPackage;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsPackageListen;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsTraceInfo;
// import cn.loveapp.logistics.onsconsumer.entity.TradeLogisticsRule;
// import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
// import com.alibaba.fastjson.JSON;
// import com.google.common.base.Strings;
// import com.google.common.collect.Lists;
// import com.google.common.io.ByteStreams;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.commons.lang3.time.FastDateFormat;
// import org.apache.commons.lang3.tuple.Pair;
// import org.junit.AfterClass;
// import org.junit.Assert;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.postgresql.PGConnection;
// import org.postgresql.copy.CopyManager;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.boot.test.mock.mockito.SpyBean;
// import org.springframework.core.io.ClassPathResource;
// import org.springframework.dao.DuplicateKeyException;
// import org.springframework.data.domain.PageRequest;
// import org.springframework.data.domain.Pageable;
// import org.springframework.data.redis.core.HashOperations;
// import org.springframework.data.redis.core.StringRedisTemplate;
// import org.springframework.data.redis.core.ValueOperations;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import java.io.File;
// import java.io.IOException;
// import java.io.InputStream;
// import java.nio.file.Files;
// import java.nio.file.Path;
// import java.nio.file.Paths;
// import java.sql.Connection;
// import java.time.Instant;
// import java.time.LocalDateTime;
// import java.time.ZoneId;
// import java.time.temporal.ChronoUnit;
// import java.util.Arrays;
// import java.util.Date;
// import java.util.List;
// import java.util.concurrent.TimeUnit;
//
// import static org.mockito.ArgumentMatchers.anyInt;
// import static org.mockito.ArgumentMatchers.anyString;
// import static org.mockito.BDDMockito.*;
//
/// **
// * LogisticsServiceImplTest
// *
// * <AUTHOR>
// * @date 2018年10月11日 下午8:26:10
// */
// @RunWith(SpringRunner.class)
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
// classes = {LogisticsServiceImpl.class, TestConfig.class})
// @ActiveProfiles("test")
// public class LogisticsServiceImplTest {
//
// @MockBean
// private StringRedisTemplate redisHelper;
//
// @MockBean
// private ValueOperations valueOperations;
//
// @MockBean
// private HashOperations hashOperations;
//
// @MockBean
// private UserProductinfoTradeDao userProductinfoTradeDao;
//
// @MockBean
// private LogisticsErrorPackageDao logisticsErrorPackageDao;
//
// @MockBean
// private LogisticsPackageListenDao logisticsPackageListenDao;
//
// @MockBean
// private TradeListDao tradeListDao;
//
// @MockBean
// private LogisticsTranceInfoDao logisticsTranceInfoDao;
//
// @MockBean
// private TradeLogisticsRuleDao tradeLogisticsRuleDao;
//
// @MockBean
// private TaobaoService taobaoService;
//
// @SpyBean
// private LogisticsServiceImpl logisticsService;
//
// // @Autowired
// // @Qualifier("logisticsDataSource")
// // private DataSource logisticsDatasource;
// //
// // @Autowired
// // public JdbcTemplate jdbcTemplate;
//
// private String sellerId = "111111";
// private int listId = Integer.parseInt(StringUtils.substring(sellerId, -2));
// private String corpId = sellerId;
// private String sellerNick = "222222";
// private String tid = "33333";
// private String outSid = "33333";
// private String companyName = "33333";
// private Date modified = Date.from(LocalDateTime.of(2019, 1, 24, 0, 0).atZone(ZoneId.systemDefault()).toInstant());
// private String action = "TRADE_SUCCESS";
// private LogisticsStatus status = LogisticsStatus.CLOSE;
// private String desc = "干线到达 | 晟邦";
//
// @Before
// public void setUp() {
// when(redisHelper.opsForValue()).thenReturn(valueOperations);
// when(redisHelper.opsForHash()).thenReturn(hashOperations);
// }
//
// @AfterClass
// public static void allDown() {
// }
//
// private LogisticsInfoDTO createLogisticsInfoDTO() {
// return new LogisticsInfoDTO(sellerNick, tid, sellerId, outSid, action, modified, desc, companyName, corpId,
// null, status,"","");
// }
//
// @Test
// public void convertToLocalDateTimeViaInstantTest() {
// Date date = new Date();
// LocalDateTime time = logisticsService.convertToLocalDateTimeViaInstant(date);
// Assert.assertEquals(Date.from(time.atZone(ZoneId.of("UTC")).toInstant()), date);
// }
//
//// @Test
//// public void saveLogisticInfo2File() throws IOException {
//// String name = "logistic_" + FastDateFormat.getInstance("yyyy-MM-dd-HH-mm").format(new Date()) + "-0";
//// doReturn(LocalDateTime.of(2019, 1, 24, 1, 1)).when(logisticsService)
//// .convertToLocalDateTimeViaInstant(any(Date.class));
//// Path file = logisticsService.saveLogisticInfo2File(createLogisticsInfoDTO());
//// try {
//// Assert.assertEquals(name, file.getFileName().toString());
//// try (InputStream inputStream = new ClassPathResource("template/logistic_write_file").getInputStream()) {
//// Assert.assertArrayEquals(ByteStreams.toByteArray(inputStream), Files.readAllBytes(file));
//// }
//// } finally {
//// Files.delete(file);
//// }
//// }
////
//// @Test
//// public void saveLogisticInfo2File2() throws IOException {
//// String name = "logistic_" + FastDateFormat.getInstance("yyyy-MM-dd-HH-mm").format(new Date()) + "-0";
//// doReturn(LocalDateTime.of(2019, 1, 24, 1, 1)).when(logisticsService)
//// .convertToLocalDateTimeViaInstant(any(Date.class));
//// //desc长于800, 自动截断
//// LogisticsInfoDTO longDescInfo = createLogisticsInfoDTO();
//// longDescInfo.setDesc(Strings.repeat("1", 1000));
//// Path file = logisticsService.saveLogisticInfo2File(longDescInfo);
//// try {
//// Assert.assertEquals(name, file.getFileName().toString());
//// try (
//// InputStream inputStream = new ClassPathResource("template/logistic_write_long_file").getInputStream()) {
//// Assert.assertArrayEquals(ByteStreams.toByteArray(inputStream), Files.readAllBytes(file));
//// }
//// } finally {
//// Files.delete(file);
//// }
//// }
//
// @Test
// public void batch2DbFromFile() throws Exception {
// File file = new File("/tmp/test-0");
// try {
// file.getParentFile().mkdirs();
// file.createNewFile();
//
// Connection connection = mock(Connection.class);
// PGConnection pgConnection = mock(PGConnection.class);
// CopyManager copyManager = mock(CopyManager.class);
// when(connection.unwrap(PGConnection.class)).thenReturn(pgConnection);
// when(pgConnection.getCopyAPI()).thenReturn(copyManager);
//
// logisticsService.batch2DbFromFile(file, connection);
//
// String tableInsert =
// "COPY BINARY logistics_trace_info_0 (seller_nick, corp_id, seller_id, list_id, tid, out_sid, company_name, modified,
// action, status, descs, gmt_create) FROM STDIN";
//
// verify(copyManager).copyIn(eq(tableInsert), any(InputStream.class));
//
// } finally {
// file.delete();
// }
//
// }
//
// @Test
// public void saveLogisticsMonitoringInfo() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// when(taobaoService.getVipflag(sellerNick)).thenReturn(true);
// doNothing().when(logisticsService).saveClosePackage(any(LogisticsInfoDTO.class));
//
// logisticsInfo.setStatus(LogisticsStatus.CLOSE);
// logisticsService.saveLogisticsMonitoringInfo(logisticsInfo);
// verify(logisticsService).saveClosePackage(eq(logisticsInfo));
//
// }
//
// @Test
// public void saveLogisticsMonitoringInfo2() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// when(taobaoService.getVipflag(sellerNick)).thenReturn(true);
// doNothing().when(logisticsService).saveClosePackage(any(LogisticsInfoDTO.class));
//
// logisticsInfo.setStatus(LogisticsStatus.SIGNED);
// logisticsService.saveLogisticsMonitoringInfo(logisticsInfo);
// verify(logisticsService).saveRunningPackage(eq(logisticsInfo));
//
// }
//
// @Test
// public void saveLogisticsMonitoringInfo3() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// when(taobaoService.getVipflag(sellerNick)).thenReturn(true);
// doNothing().when(logisticsService).saveClosePackage(any(LogisticsInfoDTO.class));
//
// when(taobaoService.getVipflag(sellerNick)).thenReturn(false);
// logisticsService.saveLogisticsMonitoringInfo(logisticsInfo);
// verify(logisticsService, never()).saveRunningPackage(eq(logisticsInfo));
//
// }
//
// @Test
// public void saveErrorPackage() {
// doNothing().when(logisticsService)
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// String errReason = "test";
// int type = LogisticsErrorPackage.TYPE_RULE;
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// when(logisticsErrorPackageDao.queryByTidAndOutSid(eq(tid), eq(outSid))).thenReturn(null);
// logisticsService.saveErrorPackage(logisticsInfo, errReason, type);
//
// verify(logisticsErrorPackageDao).insert(argThat(
// (LogisticsErrorPackage a) -> a.getSellerNick().equals(sellerNick) && a.getSellerId().equals(sellerId) && a
// .getOutSid().equals(outSid) && a.getErrorResult().equals(errReason) && a.getType().equals(type) && a
// .getStatus().equals(LogisticsErrorPackage.STATUS_OPEN) && a.getCompanyName().equals(companyName) && a
// .getMarkLog().endsWith("," + LogisticsServiceImpl.SYSTEM_AUTOMATICALLY + ",save") && !a.getMarkLog()
// .contains("||")));
//
// verify(logisticsService)
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_ERROR),
// eq(0));
// }
//
// @Test
// public void saveErrorPackage2() {
// doNothing().when(logisticsService)
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// String errReason = "test";
// int type = LogisticsErrorPackage.TYPE_RULE;
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
//
// LogisticsErrorPackage logisticsErrorPackage = new LogisticsErrorPackage();
// logisticsErrorPackage.setSellerId(sellerId);
// logisticsErrorPackage.setSellerNick(sellerNick);
// logisticsErrorPackage.setOutSid(outSid);
// logisticsErrorPackage.setCompanyName(companyName);
// logisticsErrorPackage.setStatus(LogisticsErrorPackage.STATUS_CLOSE);
//
// when(logisticsErrorPackageDao.queryByTidAndOutSid(eq(tid), eq(outSid))).thenReturn(logisticsErrorPackage);
// logisticsService.saveErrorPackage(logisticsInfo, errReason, type);
//
// verify(logisticsErrorPackageDao, never()).insert(any(LogisticsErrorPackage.class));
//
// verify(logisticsErrorPackageDao).updateById(argThat(
// (LogisticsErrorPackage a) -> a.getSellerNick().equals(sellerNick) && a.getSellerId().equals(sellerId) && a
// .getOutSid().equals(outSid) && a.getErrorResult().equals(errReason) && a.getType().equals(type) && a
// .getStatus().equals(LogisticsErrorPackage.STATUS_OPEN) && a.getCompanyName().equals(companyName) && a
// .getMarkLog().endsWith("," + LogisticsServiceImpl.SYSTEM_AUTOMATICALLY + ",save") && !a.getMarkLog()
// .contains("||")));
//
// verify(logisticsService)
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_ERROR),
// eq(0));
// }
//
// @Test
// public void saveErrorPackage3() {
// doNothing().when(logisticsService)
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// String errReason = "test";
// int type = LogisticsErrorPackage.TYPE_RULE;
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
//
// LogisticsErrorPackage logisticsErrorPackage = new LogisticsErrorPackage();
// logisticsErrorPackage.setSellerId(sellerId);
// logisticsErrorPackage.setSellerNick(sellerNick);
// logisticsErrorPackage.setOutSid(outSid);
// logisticsErrorPackage.setCompanyName(companyName);
// logisticsErrorPackage.setStatus(LogisticsErrorPackage.STATUS_CLOSE);
// logisticsErrorPackage.setMarkLog(LogisticsServiceImpl.SYSTEM_AUTOMATICALLY);
//
// when(logisticsErrorPackageDao.queryByTidAndOutSid(eq(tid), eq(outSid))).thenReturn(logisticsErrorPackage);
// logisticsService.saveErrorPackage(logisticsInfo, errReason, type);
//
// verify(logisticsErrorPackageDao, never()).insert(any(LogisticsErrorPackage.class));
//
// verify(logisticsErrorPackageDao).updateById(argThat(
// (LogisticsErrorPackage a) -> a.getSellerNick().equals(sellerNick) && a.getSellerId().equals(sellerId) && a
// .getOutSid().equals(outSid) && a.getErrorResult().equals(errReason) && a.getType().equals(type) && a
// .getStatus().equals(LogisticsErrorPackage.STATUS_OPEN) && a.getCompanyName().equals(companyName) && a
// .getMarkLog().endsWith("," + LogisticsServiceImpl.SYSTEM_AUTOMATICALLY + ",save") && a.getMarkLog()
// .startsWith(LogisticsServiceImpl.SYSTEM_AUTOMATICALLY + "||")));
//
// verify(logisticsService)
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_ERROR),
// eq(0));
// }
//
// @Test
// public void saveClosePackage() {
// doNothing().when(logisticsService)
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsService.saveClosePackage(logisticsInfo);
// verify(logisticsErrorPackageDao)
// .updateStatusByTidAndOutSid(eq(new LogisticsErrorPackage(LogisticsErrorPackage.STATUS_CLOSE, tid, outSid)));
// verify(logisticsService)
// .updatePackageListenRemark(eq(sellerNick), eq(tid), eq(outSid), eq(LogisticsPackageListen.REMARK_CLOSE),
// eq(0));
// }
//
// @Test
// public void saveClosePackage2() {
// doNothing().when(logisticsService)
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setStatus(LogisticsStatus.SIGNED);
// logisticsService.saveClosePackage(logisticsInfo);
// verify(logisticsErrorPackageDao, never()).updateStatusByTidAndOutSid(any(LogisticsErrorPackage.class));
// verify(logisticsService, never())
// .updatePackageListenRemark(anyString(), anyString(), anyString(), anyString(), anyInt());
// }
//
// @Test
// public void saveRunningPackage() {
// String province = "province";
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setStatus(LogisticsStatus.NOPACKAGE);
//
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen(province, logisticsInfo);
// logisticsPackageListen.setId(1L);
//
// doReturn(logisticsPackageListen).when(logisticsService)
// .getRedisPackageListenRecord(anyString(), anyString(), anyString(), anyInt(), anyBoolean());
//
// Pair<TradeLogisticsRule, String> errorRule = Pair.of(TradeLogisticsRule.newDefault(), province);
//
// doReturn(errorRule).when(logisticsService).getMinStatusTimeErrorRule(anyString(), any(LogisticsInfoDTO.class));
//
// logisticsService.saveRunningPackage(logisticsInfo);
//
// verify(logisticsPackageListenDao).updateByIdAndListId(any(LogisticsPackageListen.class));
//
// LogisticsPackageListen cache = new LogisticsPackageListen();
// cache.setId(logisticsPackageListen.getId());
// cache.setProvince(province);
// cache.setRemark(LogisticsPackageListen.REMARK_LISTEN);
// verify(valueOperations).set(eq(tid + outSid), eq(JSON.toJSONString(cache)), anyLong(), any(TimeUnit.class));
//
// }
//
// private LogisticsPackageListen createLogisticsPackageListen(String province, LogisticsInfoDTO logisticsInfo) {
// return new LogisticsPackageListen(logisticsInfo.getCorpId(), sellerNick, logisticsInfo.getSellerId(), tid,
// outSid, logisticsInfo.getCompanyName(), logisticsInfo.getModified(), logisticsInfo.getAction(),
// logisticsInfo.getStatus().value(), logisticsInfo.getDesc(), province, LogisticsPackageListen.REMARK_LISTEN,
// new Date(), listId);
// }
//
// @Test
// public void saveRunningPackage2() {
// String province = "province";
//
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setStatus(LogisticsStatus.NOPACKAGE);
//
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen(province, logisticsInfo);
// logisticsPackageListen.setRemark(LogisticsPackageListen.REMARK_CLOSE);
//
// doReturn(logisticsPackageListen).when(logisticsService)
// .getRedisPackageListenRecord(anyString(), anyString(), anyString(), anyInt(), anyBoolean());
//
// logisticsService.saveRunningPackage(logisticsInfo);
//
// verify(logisticsPackageListenDao, never()).updateByIdAndListId(any(LogisticsPackageListen.class));
//
// verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
//
// }
//
// @Test
// public void saveRunningPackage3() {
// String province = "province";
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setStatus(LogisticsStatus.NOPACKAGE);
//
// doReturn(null).when(logisticsService)
// .getRedisPackageListenRecord(anyString(), anyString(), anyString(), anyInt(), anyBoolean());
//
// Pair<TradeLogisticsRule, String> errorRule = Pair.of(TradeLogisticsRule.newDefault(), province);
//
// doReturn(errorRule).when(logisticsService).getMinStatusTimeErrorRule(anyString(), any(LogisticsInfoDTO.class));
//
// logisticsService.saveRunningPackage(logisticsInfo);
//
// verify(logisticsPackageListenDao).insert(any(LogisticsPackageListen.class));
//
// LogisticsPackageListen cache = new LogisticsPackageListen();
// cache.setProvince(province);
// cache.setRemark(LogisticsPackageListen.REMARK_LISTEN);
// verify(valueOperations).set(eq(tid + outSid), eq(JSON.toJSONString(cache)), anyLong(), any(TimeUnit.class));
//
// }
//
// @Test
// public void saveRunningPackage4() {
// String province = "province";
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setStatus(LogisticsStatus.NOPACKAGE);
//
// doReturn(null).when(logisticsService)
// .getRedisPackageListenRecord(anyString(), anyString(), anyString(), anyInt(), anyBoolean());
//
// Pair<TradeLogisticsRule, String> errorRule = Pair.of(TradeLogisticsRule.newDefault(), province);
//
// doReturn(errorRule).when(logisticsService).getMinStatusTimeErrorRule(anyString(), any(LogisticsInfoDTO.class));
//
// when(logisticsPackageListenDao.insert(any(LogisticsPackageListen.class)))
// .thenThrow(new DuplicateKeyException("DuplicateKeyException"));
//
// logisticsService.saveRunningPackage(logisticsInfo);
//
// verify(logisticsPackageListenDao).insert(any(LogisticsPackageListen.class));
// verify(logisticsPackageListenDao).updateByTidAndOutSidAndListId(any(LogisticsPackageListen.class));
//
// LogisticsPackageListen cache = new LogisticsPackageListen();
// cache.setProvince(province);
// cache.setRemark(LogisticsPackageListen.REMARK_LISTEN);
// verify(valueOperations).set(eq(tid + outSid), eq(JSON.toJSONString(cache)), anyLong(), any(TimeUnit.class));
//
// }
//
// @Test
// public void getMinStatusTimeErrorRule() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setProvince("province");
// Pair<TradeLogisticsRule, String> result = logisticsService.getMinStatusTimeErrorRule(null, logisticsInfo);
// Assert.assertNotNull(result);
// Assert.assertNull(result.getLeft());
// Assert.assertEquals(logisticsInfo.getProvince(), result.getRight());
// }
//
// @Test
// public void getMinStatusTimeErrorRule2() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setProvince("province");
//
// TradeLogisticsRule rule1 = TradeLogisticsRule.newDefault();
// TradeLogisticsRule rule2 = TradeLogisticsRule.newDefault();
// rule2.setCompany(companyName);
// rule2.setStop(1);
//
// doReturn(Lists.newArrayList(rule1, rule2)).when(logisticsService)
// .getSellerLogisticsErrorRule(anyString(), anyString());
// Pair<TradeLogisticsRule, String> result =
// logisticsService.getMinStatusTimeErrorRule(TradeLogisticsRule.STATUS_STOP, logisticsInfo);
// Assert.assertNotNull(result);
// Assert.assertEquals(rule2, result.getLeft());
// Assert.assertEquals(logisticsInfo.getProvince(), result.getRight());
// }
//
// @Test
// public void getMinStatusTimeErrorRule3() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// String province = "province";
// logisticsInfo.setProvince(province);
//
// TradeLogisticsRule rule1 = TradeLogisticsRule.newDefault();
// rule1.setCompany(companyName);
// rule1.setProvince(province);
// rule1.setStop(2);
//
// TradeLogisticsRule rule2 = TradeLogisticsRule.newDefault();
// rule2.setCompany(companyName);
// rule2.setProvince(province);
// rule2.setStop(1);
//
// doReturn(Lists.newArrayList(rule1, rule2)).when(logisticsService)
// .getSellerLogisticsErrorRule(anyString(), anyString());
// Pair<TradeLogisticsRule, String> result =
// logisticsService.getMinStatusTimeErrorRule(TradeLogisticsRule.STATUS_STOP, logisticsInfo);
// Assert.assertNotNull(result);
// Assert.assertEquals(rule2, result.getLeft());
// Assert.assertEquals(logisticsInfo.getProvince(), result.getRight());
// }
//
// @Test
// public void getMinStatusTimeErrorRule4() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// logisticsInfo.setProvince(null);
//
// String province = "province";
// TradeLogisticsRule rule1 = TradeLogisticsRule.newDefault();
// rule1.setCompany(companyName);
// rule1.setProvince(province);
// rule1.setStop(2);
//
// TradeLogisticsRule rule2 = TradeLogisticsRule.newDefault();
// rule2.setCompany(companyName);
// rule2.setProvince(province);
// rule2.setStop(1);
//
// doReturn(Lists.newArrayList(rule1, rule2)).when(logisticsService)
// .getSellerLogisticsErrorRule(anyString(), anyString());
//
// when(tradeListDao.queryReceiverStateBySellerNickAndTidAndListId(anyString(), anyString(), anyInt()))
// .thenReturn(province);
//
// Pair<TradeLogisticsRule, String> result =
// logisticsService.getMinStatusTimeErrorRule(TradeLogisticsRule.STATUS_STOP, logisticsInfo);
// Assert.assertNotNull(result);
// Assert.assertEquals(rule2, result.getLeft());
// Assert.assertEquals(logisticsInfo.getProvince(), result.getRight());
// }
//
// @Test
// public void getLogisticsRuleEndTime() {
// Date date = new Date();
// Date result = logisticsService.getLogisticsRuleEndTime(date, 2);
// Instant instant = date.toInstant().plus(2, ChronoUnit.DAYS);
// Assert.assertEquals(instant, result.toInstant());
// }
//
// @Test
// public void getRedisPackageListenRecord() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen("province", logisticsInfo);
// logisticsPackageListen.setId(1L);
//
// when(valueOperations.get(anyString())).thenReturn(JSON.toJSONString(logisticsPackageListen));
//
// LogisticsPackageListen result = logisticsService.getRedisPackageListenRecord(sellerNick, tid, outSid, 0, true);
//
// verify(logisticsPackageListenDao, never()).queryByTidAndOutSidAndListId(anyString(), anyString(), anyInt());
// Assert.assertEquals(logisticsPackageListen, result);
// }
//
// @Test
// public void getRedisPackageListenRecord2() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen("province", logisticsInfo);
// logisticsPackageListen.setId(1L);
//
// when(valueOperations.get(anyString())).thenReturn(null);
//
// when(logisticsPackageListenDao.queryByTidAndOutSidAndListId(eq(tid), eq(outSid), eq(0)))
// .thenReturn(logisticsPackageListen);
//
// LogisticsPackageListen result = logisticsService.getRedisPackageListenRecord(sellerNick, tid, outSid, 0, true);
//
// verify(valueOperations).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
// Assert.assertEquals(logisticsPackageListen, result);
// }
//
// @Test
// public void getRedisPackageListenRecord3() {
// LogisticsInfoDTO logisticsInfo = createLogisticsInfoDTO();
// LogisticsPackageListen logisticsPackageListen = createLogisticsPackageListen("province", logisticsInfo);
// logisticsPackageListen.setId(1L);
//
// when(valueOperations.get(anyString())).thenReturn(null);
//
// when(logisticsPackageListenDao.queryByTidAndOutSidAndListId(eq(tid), eq(outSid), eq(0)))
// .thenReturn(logisticsPackageListen);
//
// LogisticsPackageListen result = logisticsService.getRedisPackageListenRecord(sellerNick, tid, outSid, 0, false);
//
// verify(valueOperations, never()).set(anyString(), anyString(), anyLong(), any(TimeUnit.class));
// Assert.assertEquals(logisticsPackageListen, result);
// }
//
// @Test
// public void updatePackageListenRemark() {
// logisticsService.updatePackageListenRemark(sellerNick, tid, outSid, null, 0);
// verify(logisticsPackageListenDao, never())
// .updateRemarkByTidAndOutSidAndListId(any(LogisticsPackageListen.class));
// }
//
// @Test
// public void updatePackageListenRemark2() {
// String remark = LogisticsPackageListen.REMARK_LISTEN;
// Date endTime = new Date();
// LogisticsPackageListen listen = createLogisticsPackageListen("province", createLogisticsInfoDTO());
// listen.setEndTime(endTime);
// doReturn(listen).when(logisticsService).getRedisPackageListenRecord(sellerNick, tid, outSid, 0, false);
//
// logisticsService.updatePackageListenRemark(sellerNick, tid, outSid, remark, 0);
// verify(logisticsPackageListenDao)
// .updateRemarkByTidAndOutSidAndListId(eq(new LogisticsPackageListen(remark, tid, outSid, 0)));
//
// listen = createLogisticsPackageListen("province", createLogisticsInfoDTO());
// listen.setRemark(remark);
// listen.setEndTime(endTime);
// verify(valueOperations).set(eq(tid + outSid), eq(JSON.toJSONString(listen)), anyLong(), any(TimeUnit.class));
//
// }
//
// @Test
// public void findAllFromPackageListen() {
// String remark = LogisticsPackageListen.REMARK_LISTEN;
// List<LogisticsPackageListen> list =
// Lists.newArrayList(createLogisticsPackageListen("province", createLogisticsInfoDTO()));
// when(logisticsPackageListenDao.pageQueryByRemarkAndListIdBeforeNow(eq(remark), anyInt(), any(Pageable.class)))
// .thenReturn(list);
//
// List<LogisticsPackageListen> result =
// logisticsService.findAllFromPackageListen(remark, 0, PageRequest.of(1, 1));
// Assert.assertEquals(list, result);
// }
//
// @Test
// public void getSellerLogisticsErrorRule() {
// when(hashOperations.get(anyString(), eq("logistics_rule"))).thenReturn(LogisticsServiceImpl.RULE_DEFAULT_VALUE);
//
// List<TradeLogisticsRule> result = logisticsService.getSellerLogisticsErrorRule(sellerNick, tid);
//
// Assert.assertNotNull(result);
// Assert.assertEquals(1, result.size());
// Assert.assertEquals(TradeLogisticsRule.newDefault(), result.get(0));
// }
//
// @Test
// public void getSellerLogisticsErrorRule1() {
// List<TradeLogisticsRule> list = Lists.newArrayList(TradeLogisticsRule.newDefault());
// when(hashOperations.get(anyString(), eq("logistics_rule"))).thenReturn(JSON.toJSONString(list));
//
// List<TradeLogisticsRule> result = logisticsService.getSellerLogisticsErrorRule(sellerNick, tid);
//
// Assert.assertEquals(list, result);
// }
//
// @Test
// public void getSellerLogisticsErrorRule2() {
// List<TradeLogisticsRule> list = Lists.newArrayList(TradeLogisticsRule.newDefault());
// when(hashOperations.get(anyString(), eq("logistics_rule"))).thenReturn(JSON.toJSONString(list));
//
// List<TradeLogisticsRule> result = logisticsService.getSellerLogisticsErrorRule(sellerNick, tid);
//
// Assert.assertNotNull(result);
// Assert.assertEquals(list, result);
// }
//
// @Test
// public void getSellerLogisticsErrorRule3() {
// when(hashOperations.get(anyString(), eq("logistics_rule"))).thenReturn(null);
// when(tradeLogisticsRuleDao.queryByNick(anyString())).thenReturn(null);
//
// List<TradeLogisticsRule> result = logisticsService.getSellerLogisticsErrorRule(sellerNick, tid);
//
// verify(hashOperations).put(anyString(), eq("logistics_rule"), eq(LogisticsServiceImpl.RULE_DEFAULT_VALUE));
//
// Assert.assertNotNull(result);
// Assert.assertEquals(1, result.size());
// Assert.assertEquals(TradeLogisticsRule.newDefault(), result.get(0));
// }
//
// @Test
// public void getSellerLogisticsErrorRule4() {
// List<TradeLogisticsRule> list = Lists.newArrayList(TradeLogisticsRule.newDefault());
// when(hashOperations.get(anyString(), eq("logistics_rule"))).thenReturn(null);
// when(tradeLogisticsRuleDao.queryByNick(anyString())).thenReturn(list);
//
// List<TradeLogisticsRule> result = logisticsService.getSellerLogisticsErrorRule(sellerNick, tid);
//
// verify(hashOperations).put(anyString(), eq("logistics_rule"), eq(JSON.toJSONString(list)));
//
// Assert.assertNotNull(result);
// Assert.assertEquals(1, result.size());
// Assert.assertEquals(TradeLogisticsRule.newDefault(), result.get(0));
// }
//
// @Test
// public void getLastLogisticsAction() {
// LogisticsInfoDTO dto = new LogisticsInfoDTO();
// dto.setTid(tid);
// dto.setOutSid(outSid);
// dto.setCorpId("111111");
//
// LogisticsTraceInfo info1 = new LogisticsTraceInfo();
// info1.setAction("1");
// info1.setModified(LocalDateTime.now());
//
// LogisticsTraceInfo info2 = new LogisticsTraceInfo();
// info2.setAction("2");
// info2.setModified(LocalDateTime.now().minusDays(1));
//
// LogisticsTraceInfo max = new LogisticsTraceInfo();
// max.setAction("max");
// max.setModified(LocalDateTime.now().plusDays(1));
//
// LogisticsTraceInfo info4 = new LogisticsTraceInfo();
// info4.setAction("4");
// info4.setModified(LocalDateTime.now().minusDays(10));
//
// List<LogisticsTraceInfo> list = Arrays.asList(info1, info2, max, info4);
//
// when(logisticsTranceInfoDao.queryByTidAndOutSid(anyString(), anyString(), anyInt())).thenReturn(list);
//
// String action = logisticsService.getLastLogisticsAction(dto);
//
// Assert.assertEquals(max.getAction(), action);
// }
//
// @Test
// public void getLastLogisticsAction1() {
// String action = logisticsService.getLastLogisticsAction(null);
//
// Assert.assertNull(action);
// }
//
// @Test
// public void getLastLogisticsAction2() {
// when(logisticsTranceInfoDao.queryByTidAndOutSid(anyString(), anyString(), anyInt())).thenReturn(null);
// String action = logisticsService.getLastLogisticsAction(createLogisticsInfoDTO());
//
// Assert.assertNull(action);
// }
// }

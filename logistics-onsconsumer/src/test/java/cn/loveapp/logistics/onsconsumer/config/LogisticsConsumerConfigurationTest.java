package cn.loveapp.logistics.onsconsumer.config;

import java.io.IOException;
import java.io.InputStream;

import cn.loveapp.logistics.common.config.LogisticsActions;
import cn.loveapp.logistics.common.config.LogisticsConsumerConfiguration;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.io.ClassPathResource;
import org.springframework.test.context.junit4.SpringRunner;
import org.yaml.snakeyaml.Yaml;

import cn.loveapp.logistics.onsconsumer.TestConfig;

@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
    classes = {LogisticsConsumerConfiguration.class, TestConfig.class})
public class LogisticsConsumerConfigurationTest {

    @Autowired
    private LogisticsConsumerConfiguration logisticsConsumerConfiguration;

    @Test
    public void logisticsActions() throws IOException {
        LogisticsActions actions = logisticsConsumerConfiguration.logisticsActions();
        Yaml yaml = new Yaml();
        Iterable<Object> itsSrc = yaml.loadAll(yaml.dump(actions));
        try (InputStream inputStream = new ClassPathResource("logisitics_actions.yml").getInputStream()) {
            Iterable<Object> itsDist = yaml.loadAll(inputStream);
            Assert.assertFalse(itsSrc.equals(itsDist));
        }
    }
}

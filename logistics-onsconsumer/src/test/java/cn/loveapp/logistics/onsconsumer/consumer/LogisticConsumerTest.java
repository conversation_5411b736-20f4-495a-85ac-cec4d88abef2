// package cn.loveapp.logistics.onsconsumer.consumer;
//
// import cn.loveapp.logistics.onsconsumer.TestConfig;
// import cn.loveapp.logistics.onsconsumer.config.LogisticsConfig;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsInfoDTO;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsStatus;
// import cn.loveapp.logistics.onsconsumer.service.LogisticsService;
// import cn.loveapp.logistics.common.service.UserInfoService;
// import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
// import cn.loveapp.uac.request.UserInfoRequest;
// import cn.loveapp.uac.response.UserInfoResponse;
// import cn.loveapp.uac.service.UserCenterInnerApiService;
// import com.alibaba.fastjson.JSONObject;
// import com.ctrip.framework.apollo.enums.PropertyChangeType;
// import com.ctrip.framework.apollo.model.ConfigChange;
// import com.ctrip.framework.apollo.model.ConfigChangeEvent;
// import com.google.common.collect.Lists;
// import org.apache.commons.lang3.StringUtils;
// import org.apache.commons.lang3.time.FastDateFormat;
// import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyContext;
// import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
// import org.apache.rocketmq.client.exception.MQClientException;
// import org.apache.rocketmq.client.producer.DefaultMQProducer;
// import org.apache.rocketmq.client.producer.SendResult;
// import org.apache.rocketmq.common.message.Message;
// import org.apache.rocketmq.common.message.MessageExt;
// import org.apache.rocketmq.common.message.MessageQueue;
// import org.junit.Assert;
// import org.junit.Before;
// import org.junit.Test;
// import org.junit.runner.RunWith;
// import org.mockito.BDDMockito;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.boot.test.mock.mockito.MockBeans;
// import org.springframework.boot.test.mock.mockito.SpyBean;
// import org.springframework.context.event.ContextClosedEvent;
// import org.springframework.test.context.junit4.SpringRunner;
//
// import java.text.ParseException;
// import java.text.SimpleDateFormat;
// import java.util.Collections;
// import java.util.Date;
//
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.BDDMockito.*;
//
/// **
// * LogisticConsumerTest
// *
// * <AUTHOR>
// * @date 2018年10月10日 下午3:29:02
// */
// @RunWith(SpringRunner.class)
// @MockBeans({@MockBean(QpsJob.class)})
// @SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.NONE,
// classes = {LogisticConsumer.class, LogisticsConfig.class, TestConfig.class})
// public class LogisticConsumerTest {
// private final static FastDateFormat FAST_DATE_FORMAT = FastDateFormat.getInstance("yyyy-MM-dd HH:mm:ss");
// @MockBean
// private LogisticsService pgLogisticsService;
//
// @MockBean
// private TaobaoService taobaoService;
//
// @MockBean
// private DefaultMQProducer logisticsOnsProducer;
//
// @MockBean
// private UserInfoService userInfoService;
//
// @MockBean
// private UserCenterInnerApiService userCenterInnerApiService;
//
// @SpyBean
// private LogisticConsumer logisticConsumer;
//
// @Value("${logistics.taobao.ons.sms.topic}")
// private String smsTopic;
//
// @Value("${logistics.taobao.ons.sms.tag}")
// private String smsTag;
//
// @Value("${logistics.taobao.ons.sms.consumerurl}")
// private String smsConsumerUrl;
//
// private String nick = "nick";
// private String tid = "111111";
// private String user_id = "222222";
// private String seller_id = "222222";
// private String out_sid = "333333";
// private Date time = FAST_DATE_FORMAT.parse(FAST_DATE_FORMAT.format(new Date()));
// private String company_name = "company_name";
// private String desc = "desc";
// private LogisticsInfoDTO logisticsInfoUnknown = null;
// private LogisticsInfoDTO logisticsInfo = null;
// private String content = null;
// private MessageExt message = null;
// private JSONObject messageJson = null;
// private JSONObject notifyLogistics = null;
//
// public LogisticConsumerTest() throws ParseException {
// }
//
// @Before
// public void setUp() throws Exception {
// when(taobaoService.getVipflag(anyString())).thenReturn(true);
// logisticConsumer.enableCheckSigned = false;
// logisticConsumer.stop = false;
// }
//
// /**
// * 正常物流Consume
// *
// * <AUTHOR>
// * @date 2018年10月10日 下午3:27:50
// */
// @Test
// public void consume() throws Exception {
// // ------- 正常信息校验 -----------
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
// mockConsumeMessage("SENT_CITY");
//
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer).sendSms(eq(messageJson.toJSONString()), eq(messageJson), eq(logisticsInfo));
// verify(pgLogisticsService).saveLogisticInfo(eq(logisticsInfo));
// verify(pgLogisticsService).saveLogisticsMonitoringInfo(eq(logisticsInfo));
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
//
// // ------- 未知物流action信息校验 -----------
// doCallRealMethod().when(logisticConsumer).sendSms(anyString(), any(), any());
// mockConsumeMessage("11112312312");
//
// result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new ConsumeConcurrentlyContext(new
// MessageQueue()));
// verify(logisticsOnsProducer, never()).send(any(Message.class));
// verify(pgLogisticsService).saveLogisticInfo(eq(logisticsInfo));
// verify(pgLogisticsService).saveLogisticsMonitoringInfo(eq(logisticsInfo));
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
//
// }
//
// @Test
// public void consume2() throws Exception {
// UserInfoRequest request = new UserInfoRequest();
// request.setSellerId("*********");
// request.setApp("guanDian");
// request.setPlatformId("PDD");
//
// UserInfoResponse response = new UserInfoResponse();
// response.setSellerNick("pdd*********97");
// when(userInfoService.getSellerInfo(request)).thenReturn(response);
//
// MessageExt messageExt2 = new MessageExt();
// String message = "{\"notify_logistics\":{\"store_id\":\"trace\",
// \"platform_id\":\"PDD\",\"corpId\":*********,\"appName\":\"guanDian\",\"tid\":\"78134379695889\",\"nick\":\"\",\"province\":\"\",\"user_id\":*********,\"out_sid\":\"78134379695889\",\"company_name\":115,\"action\":\"IN_CABINET\",\"modified\":\"2020-06-18
// 11:34:18\",\"topic\":\"isv\",\"time\":\"2020-06-15
// 16:54:46\",\"tag\":\"NotifyByMall\",\"desc\":\"已送达4\",\"status\":\"\"}}";
// byte[] body = message.getBytes();
// messageExt2.setBody(body);
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(messageExt2),
// new ConsumeConcurrentlyContext(new MessageQueue()));
//
// LogisticsInfoDTO logisticsInfoDTO = new LogisticsInfoDTO();
// logisticsInfoDTO.setSellerNick("pdd*********97");
// logisticsInfoDTO.setTid("78134379695889");
// logisticsInfoDTO.setSellerId("*********");
// logisticsInfoDTO.setOutSid("78134379695889");
// logisticsInfoDTO.setAction("IN_CABINET");
// logisticsInfoDTO.setDesc("已送达4");
// logisticsInfoDTO.setCompanyName("115");
// logisticsInfoDTO.setCorpId("*********");
// logisticsInfoDTO.setStatus(LogisticsStatus.valueOf(2));
// logisticsInfoDTO.setAppName("guanDian");
// logisticsInfoDTO.setPlatformId("PDD");
// Date date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2020-06-15 16:54:46");
// logisticsInfoDTO.setModified(date);
// logisticsInfoDTO.setProvince("");
// verify(pgLogisticsService).saveLogisticInfo(eq(logisticsInfoDTO));
// verify(pgLogisticsService).saveLogisticsMonitoringInfo(eq(logisticsInfoDTO));
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
// }
//
//// /**
//// * 停止consume
//// *
//// * @throws Exception
//// */
//// @Test
//// public void stopConsume() throws Exception {
//// //spy sms
//// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
//// mockConsumeMessage("SENT_CITY");
////
//// // ------- 停止校验 -----------
//// logisticConsumer.stop = true;
//// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
//// verify(logisticConsumer, never()).sendSms(anyString(), any(), any());
//// Assert.assertEquals(ConsumeConcurrentlyStatus.RECONSUME_LATER, result);
//// logisticConsumer.stop = false;
////
//// }
//
// /**
// * 停止处理异常物流
// *
// * @throws Exception
// */
// @Test
// public void stopErrorConsume() throws Exception {
// //spy sms
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
// mockConsumeMessage("SENT_CITY");
//
// logisticConsumer.errorEnable = false;
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer).sendSms(messageJson.toJSONString(), messageJson, logisticsInfo);
// verify(pgLogisticsService).saveLogisticInfo(logisticsInfo);
// verify(pgLogisticsService, never()).saveLogisticsMonitoringInfo(logisticsInfo);
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
// logisticConsumer.errorEnable = true;
//
// }
//
// /**
// * 接收到非json格式的物流通知
// *
// * @throws Exception
// */
// @Test
// public void consumeErrorJsonNotifyLogistics() throws Exception {
// //spy sms
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
//
// //缺少notify_logistics
// mockConsumeMessage("SENT_CITY");
// given(message.getBody()).willReturn("{}".getBytes());
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer, never()).sendSms(anyString(), any(), any());
// verify(pgLogisticsService, never()).saveLogisticInfo(any());
// verify(pgLogisticsService, never()).saveLogisticsMonitoringInfo(any());
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
// }
//
// /**
// * 接收到缺少notify_logistics的物流通知
// *
// * @throws Exception
// */
// @Test
// public void consumeLessNotifyLogistics() throws Exception {
// //spy sms
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
//
// //缺少notify_logistics
// mockConsumeMessage("SENT_CITY");
// messageJson.remove("notify_logistics");
// given(message.getBody()).willReturn(messageJson.toJSONString().getBytes());
//
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer, never()).sendSms(anyString(), any(JSONObject.class), any(LogisticsInfoDTO.class));
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
// }
//
// /**
// * 接收到缺少out_sid的物流通知
// *
// * @throws Exception
// */
// @Test
// public void consumeLessOutSid() throws Exception {
// //spy sms
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
//
// //缺少out_sid
// mockConsumeMessage("SENT_CITY");
// notifyLogistics.put("out_sid", StringUtils.EMPTY);
// logisticsInfoUnknown.setOutSid(StringUtils.EMPTY);
// given(message.getBody()).willReturn(messageJson.toJSONString().getBytes());
//
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer).sendSms(messageJson.toJSONString(), messageJson, logisticsInfoUnknown);
// verify(pgLogisticsService, never()).saveLogisticInfo(logisticsInfo);
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
// }
//
// /**
// * 接收到缺少seller_id的物流通知
// *
// * @throws Exception
// */
// @Test
// public void consumeLessSellerId() throws Exception {
// //spy sms
// doNothing().when(logisticConsumer).sendSms(anyString(), any(), any());
//
//
// //未查询到seller_id
// mockConsumeMessage("SENT_CITY");
// notifyLogistics.put("user_id", StringUtils.EMPTY);
// logisticsInfo.setSellerId(null);
// logisticsInfoUnknown.setSellerId(null);
// logisticsInfoUnknown.setCorpId(null);
// given(message.getBody()).willReturn(messageJson.toJSONString().getBytes());
//
// ConsumeConcurrentlyStatus result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new
// ConsumeConcurrentlyContext(new MessageQueue()));
// verify(logisticConsumer).sendSms(messageJson.toJSONString(), messageJson, logisticsInfoUnknown);
// verify(taobaoService).getUserId(nick, tid);
// verify(pgLogisticsService, never()).saveLogisticInfo(logisticsInfo);
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
//
// //查询到seller_id
// mockConsumeMessage("SENT_CITY");
// notifyLogistics.put("user_id", StringUtils.EMPTY);
// logisticsInfo.setSellerId(null);
// logisticsInfoUnknown.setCorpId(null);
// logisticsInfoUnknown.setSellerId(null);
// given(message.getBody()).willReturn(messageJson.toJSONString().getBytes());
// when(taobaoService.getUserId(nick, tid)).thenReturn(seller_id);
//
// result = logisticConsumer.consumeMessage(Lists.newArrayList(message), new ConsumeConcurrentlyContext(new
// MessageQueue()));
// verify(logisticConsumer).sendSms(messageJson.toJSONString(), messageJson, logisticsInfoUnknown);
// verify(taobaoService, times(2)).getUserId(nick, tid);
//
// logisticsInfo.setSellerId(seller_id);
// verify(pgLogisticsService).saveLogisticInfo(logisticsInfo);
// Assert.assertEquals(ConsumeConcurrentlyStatus.CONSUME_SUCCESS, result);
//
// }
//
// /**
// * mock ons消息接收
// *
// * <AUTHOR>
// * @date 2018年10月10日 下午3:29:05
// */
// private void mockConsumeMessage(String action) throws ParseException {
// message = BDDMockito.mock(MessageExt.class);
// messageJson = new JSONObject();
// notifyLogistics = new JSONObject();
// notifyLogistics.put("nick", nick);
// notifyLogistics.put("tid", tid);
// notifyLogistics.put("user_id", user_id);
//// notifyLogistics.put("corp_id", user_id);
// notifyLogistics.put("out_sid", out_sid);
// notifyLogistics.put("action", action);
// time = FAST_DATE_FORMAT.parse(FAST_DATE_FORMAT.format(new Date()));
// notifyLogistics.put("time", FAST_DATE_FORMAT.format(time));
// notifyLogistics.put("company_name", company_name);
// notifyLogistics.put("desc", desc);
// messageJson.put("notify_logistics", notifyLogistics);
// content = messageJson.toJSONString();
// BDDMockito.given(message.getBody()).willReturn(content.getBytes());
//
// LogisticsStatus status = LogisticsStatus.TRANSPORTATION;
// logisticsInfo =
// new LogisticsInfoDTO(nick, tid, seller_id, out_sid, action, time, desc, company_name, user_id, null,
// status, null, null);
// logisticsInfoUnknown =
// new LogisticsInfoDTO(nick, tid, seller_id, out_sid, action, time, desc, company_name, user_id, null,
// LogisticsStatus.UNKNOWN, null, null);
//
// }
//
// @Test
// public void configChangeLisenner() {
// String newValue = "10000";
//
// //默认设置为9999
// logisticConsumer.rateLimiter.setRate(9999);
//
// //校验正常值
// ConfigChangeEvent event = new ConfigChangeEvent("", Collections.singletonMap(LogisticConsumer.LIMIT_CONFIG_KEY,
// new ConfigChange("", LogisticConsumer.LIMIT_CONFIG_KEY, "10000", newValue, PropertyChangeType.MODIFIED)));
// logisticConsumer.configChangeLisenner(event);
//
// Assert.assertEquals(Double.parseDouble(newValue), logisticConsumer.rateLimiter.getRate(), 1);
//
// //校验值小于0时
// event = new ConfigChangeEvent("", Collections.singletonMap(LogisticConsumer.LIMIT_CONFIG_KEY,
// new ConfigChange("", LogisticConsumer.LIMIT_CONFIG_KEY, "10000", "-1", PropertyChangeType.MODIFIED)));
// logisticConsumer.configChangeLisenner(event);
//
// Assert.assertEquals(Double.parseDouble(newValue), logisticConsumer.rateLimiter.getRate(), 1);
//
//
// //校验值不是数字时
// event = new ConfigChangeEvent("", Collections.singletonMap(LogisticConsumer.LIMIT_CONFIG_KEY,
// new ConfigChange("", LogisticConsumer.LIMIT_CONFIG_KEY, "10000", "test", PropertyChangeType.MODIFIED)));
// logisticConsumer.configChangeLisenner(event);
//
// Assert.assertEquals(Double.parseDouble(newValue), logisticConsumer.rateLimiter.getRate(), 1);
//
//
// //错误的属性名
// event = new ConfigChangeEvent("", Collections.singletonMap("error",
// new ConfigChange("", "error", "10000", "test", PropertyChangeType.MODIFIED)));
// logisticConsumer.configChangeLisenner(event);
//
// Assert.assertEquals(Double.parseDouble(newValue), logisticConsumer.rateLimiter.getRate(), 1);
// }
//
// @Test
// public void onApplicationEvent() {
// logisticConsumer.stop = false;
// logisticConsumer.onApplicationEvent(BDDMockito.mock(ContextClosedEvent.class));
// Assert.assertTrue(logisticConsumer.stop);
// logisticConsumer.stop = false;
// }
//
// @Test
// public void sendSms() throws Exception {
// when(logisticsOnsProducer.send(any(Message.class))).thenReturn(new SendResult());
//
// //禁止发送短信
// logisticConsumer.enableSms = false;
// mockConsumeMessage("SENT_CITY");
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, never()).send(any(Message.class));
//
// //校验异常action
// logisticConsumer.enableSms = true;
// mockConsumeMessage("DEPARTURE");
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, never()).send(any(Message.class));
//
//
// //校验正常action
// mockConsumeMessage("SENT_CITY");
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer).send(any(Message.class));
// mockConsumeMessage("SENT_SCAN");
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(2)).send(any(Message.class));
// mockConsumeMessage("SIGNED");
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(3)).send(any(Message.class));
//
// //异常重试测试
// when(logisticsOnsProducer.send(any(Message.class))).thenThrow(MQClientException.class);
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(7)).send(any(Message.class));
//
// //校验
// logisticConsumer.enableCheckSigned = true;
// mockConsumeMessage("SIGNED");
// when(logisticsOnsProducer.send(any(Message.class))).thenReturn(new SendResult());
// doReturn(LogisticConsumer.STA_TOWN_IN).when(pgLogisticsService).getLastLogisticsAction(eq(logisticsInfo));
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(7)).send(any(Message.class));
//
// //校验
// logisticConsumer.enableCheckSigned = true;
// mockConsumeMessage("SIGNED");
// when(logisticsOnsProducer.send(any(Message.class))).thenReturn(new SendResult());
// doReturn(null).when(pgLogisticsService).getLastLogisticsAction(eq(logisticsInfo));
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(8)).send(any(Message.class));
//
// //校验
// logisticConsumer.enableCheckSigned = false;
// mockConsumeMessage("SIGNED");
// when(logisticsOnsProducer.send(any(Message.class))).thenReturn(new SendResult());
// doReturn(LogisticConsumer.STA_TOWN_IN).when(pgLogisticsService).getLastLogisticsAction(eq(logisticsInfo));
// logisticConsumer.sendSms(content, messageJson, logisticsInfo);
// verify(logisticsOnsProducer, times(9)).send(any(Message.class));
//
//
//
//
//
// }
//
// @Test
// public void getStatus() {
// LogisticsStatus status = logisticConsumer.getStatus("CREATE");
// Assert.assertEquals(LogisticsStatus.NOPACKAGE, status);
//
// status = logisticConsumer.getStatus("TRADE_SUCCESS");
// Assert.assertEquals(LogisticsStatus.CLOSE, status);
//
// status = logisticConsumer.getStatus("TMS_ACCEPT");
// Assert.assertEquals(LogisticsStatus.TRANSPORTATION, status);
//
// status = logisticConsumer.getStatus("SIGNED");
// Assert.assertEquals(LogisticsStatus.SIGNED, status);
//
// status = logisticConsumer.getStatus("FAILED");
// Assert.assertEquals(LogisticsStatus.TRANSPORTATION, status);
//
// status = logisticConsumer.getStatus("TRADE_WAREHOUSE_RECEIVE");
// Assert.assertEquals(LogisticsStatus.UNKNOWN, status);
//
// status = logisticConsumer.getStatus("111");
// Assert.assertEquals(LogisticsStatus.TRANSPORTATION, status);
// }
// }

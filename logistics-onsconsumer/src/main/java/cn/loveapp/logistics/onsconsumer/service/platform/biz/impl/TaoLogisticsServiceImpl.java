package cn.loveapp.logistics.onsconsumer.service.platform.biz.impl;

import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.common.config.LogisticsActions;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.entity.mongo.LogisticsTraceInfo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;


/**
 * Tao物流入库前处理
 *
 * <AUTHOR>
 * @Date 2023/6/26 10:02
 */
@Service
public class TaoLogisticsServiceImpl extends AbstractLogisticsServiceImpl {

    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaoLogisticsServiceImpl.class);

    public static final String SIGNED = "SIGNED";
    public static final String STA_TOWN_IN = "STA_TOWN_IN";


    @Autowired
    private LogisticsActions logisticsActions;

    @Autowired
    @Qualifier("smsDefaultProducer")
    private DefaultMQProducer smsProducer;

    /**
     * 物流映射保存开关
     */
    @Value("${logistics.action.save.enable:true}")
    private boolean actionSaveEnable;

    @Value("${logistics.sms.enable:false}")
    protected boolean enableSms;

    @Value("${logistics.sms.checkSigned:false}")
    protected boolean enableCheckSigned;

    @Value("${logistics.taobao.ons.sms.topic}")
    private String smsTopic;

    @Value("${logistics.taobao.ons.sms.tag}")
    private String smsTag;

    @Value("${logistics.taobao.ons.sms.consumerurl}")
    private String smsConsumerUrl;


    @Override
    protected LogisticsTraceInfo generalLogisticsTraceInfo(LogisticsInfoDTO logisticsInfo) {
        return LogisticsTraceInfo.fromLogisticsInfoDto(logisticsInfo);
    }

    @Override
    public boolean saveLogisticInfo(List<LogisticsInfoDTO> logisticsInfos, LogisticsTraceRequestProto oMsg,
        Boolean isAbnormalAutoCheckUser, boolean checkModified, String logisticsStoreId, String logisticsAppName)
        throws IOException {
        boolean success = super.saveLogisticInfo(logisticsInfos, oMsg, isAbnormalAutoCheckUser, checkModified,
            logisticsStoreId, logisticsAppName);
        // 只有一条才发短信
        if (logisticsInfos.size() == 1) {
            try {
                // 发送短信
                for (LogisticsInfoDTO logisticsInfo : logisticsInfos) {
                    String sellerNick = logisticsInfo.getSellerNick();
                    String tid = logisticsInfo.getTid();
                    sendSms(oMsg, logisticsInfo);
                    if (StringUtils.isEmpty(logisticsInfo.getOutSid()) || logisticsInfo.getOutSid().equals(tid)) {
                        if (LOGGER.isDebugEnabled()) {
                            LOGGER.logDebug(sellerNick, tid, " 没有物流单号，尚未发货,跳过 ");
                        }
                    }
                }
            } catch (Exception e) {
                LOGGER.logError("物流单发送短信失败");
            }
        }

        return success;
    }

    /**
     * 发送短信请求
     *
     * @param logisticsTraceRequestProto
     * @param logisticsNotify
     * <AUTHOR>
     * @date 2018年10月11日 下午8:43:41
     */
    protected void sendSms(LogisticsTraceRequestProto logisticsTraceRequestProto, LogisticsInfoDTO logisticsNotify) {
        if (needSendSms(logisticsNotify)) {
            String sellerNick = logisticsNotify.getSellerNick();
            String tid = logisticsNotify.getTid();

            JSONObject oMsg = LogisticsTraceRequestProto.toOriginalJson(logisticsTraceRequestProto);
            oMsg.put("nick", sellerNick);
            oMsg.put("tid", tid);
            // 当符合这几种情况时，需要发送物流短信，提交ONS
            int retry = 4;
            for (int i = 0; i < retry; i++) {
                try {
                    Map<String, Object> map = Maps.newHashMap();
                    map.put("consumerurl", smsConsumerUrl);
                    map.put("parm", oMsg);
                    map.put("nick", sellerNick);
                    Map<String, Object> mapParm = Maps.newHashMap();
                    mapParm.put("custom", map);
                    mapParm.put("nick", sellerNick);
                    String jsonParm = JSON.toJSONString(mapParm);
                    byte[] msgBytes = jsonParm.getBytes(StandardCharsets.UTF_8);
                    Message onsMsg = new Message(
                        // Message Topic
                        smsTopic,
                        // Message Tag,
                        // 可理解为Gmail中的标签，对消息进行再归类，方便Consumer指定过滤条件在ONS服务器过滤
                        smsTag,
                        // Message Body
                        // 任何二进制形式的数据，ONS不做任何干预，需要Producer与Consumer协商好一致的序列化和反序列化方式
                        msgBytes);
                    SendResult sr = smsProducer.send(onsMsg);
                    if (LOGGER.isDebugEnabled()) {
                        LOGGER.logDebug(sellerNick, tid, "HTTP Send to Sms: " + jsonParm + ", return " + sr.getMsgId());
                    }
                } catch (MQClientException | RemotingException | MQBrokerException | InterruptedException e) {
                    if (i < retry - 1) {
                        // 失败重试
                        LOGGER.logWarn(sellerNick, tid, "RocketMQ 发送短信消息失败, 开始第" + (i + 1) + "重试 :" + JSONObject.toJSONString(oMsg));
                        try {
                            Thread.sleep(1000 * (i + 1));
                        } catch (InterruptedException ex) {
                        }
                    } else {
                        // 失败重试
                        LOGGER.logError(sellerNick, tid, "RocketMQ 发送短信消息最终失败 :" + JSONObject.toJSONString(oMsg), e);
                    }
                    continue;
                }
                break;
            }
        }
    }

    /**
     * 是否需要发送短信
     *
     * @param logisticsNotify
     * @return
     */
    protected boolean needSendSms(LogisticsInfoDTO logisticsNotify) {
        if (enableCheckSigned && SIGNED.equalsIgnoreCase(logisticsNotify.getAction())) {
            String lastAction = getLastLogisticsAction(logisticsNotify, logisticsNotify.getPlatformId(), logisticsNotify.getAppName());
            // 上个action是STA_TOWN_IN时, 基本是菜鸟乡村的快递员自己代收的情况, 不发短信
            if (STA_TOWN_IN.equalsIgnoreCase(lastAction)) {
                LOGGER.logInfo(logisticsNotify.getSellerNick(), logisticsNotify.getTid(),
                    "上次是STA_TOWN_IN, 快递员代收, 不发送签收短信");
                return false;
            }
        }
        return true;
    }

    @Override
    public void stop(String logisticsStoreId, String logisticsAppName) {

    }

    @Override
    public String getPlatformId() {
        return CommonLogisticsConstants.PLATFORM_TAO;
    }
}

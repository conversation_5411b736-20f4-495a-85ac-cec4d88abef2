package cn.loveapp.logistics.onsconsumer.config;

import javax.annotation.PreDestroy;

import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsOrderConfig;
import cn.loveapp.logistics.onsconsumer.consumer.LogisticsOrderConsumer;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.onsconsumer.consumer.LogisticConsumer;
import lombok.Setter;

/**
 * ONSConfig
 *
 * <AUTHOR>
 * @date 2018/9/21
 */
@Configuration
public class RocketMqConfig {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqConfig.class);

    private DefaultMQProducer smsProducer = null;
    private DefaultMQPushConsumer logisticsMQConsumer = null;

    private DefaultMQPushConsumer logisticsOrderMQConsumer = null;

    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private RocketMQLogisticsOrderConfig rocketMQLogisticsOrderConfig;

    @Bean(destroyMethod = "", name = "smsDefaultProducer")
    public DefaultMQProducer smsDefaultProducer() {
        // 启动ONS消息队列
        try {
            smsProducer = new DefaultMQProducer(logisticsConfig.getSmsProducerId());
            smsProducer.setSendMsgTimeout(5000);
            smsProducer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        } catch (Exception e) {
            LOGGER.logError("create sms Rocket Producer failed", e);
        }
        return smsProducer;
    }

    @Bean(destroyMethod = "", name = "logisticsMQConsumer")
    public DefaultMQPushConsumer logisticsRocketMqConsumer() {
        logisticsMQConsumer = new DefaultMQPushConsumer(logisticsConfig.getGroupId());
        logisticsMQConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsMQConsumer.setConsumeThreadMax(logisticsConfig.getLogisticsConsumeThreadNums());
        logisticsMQConsumer.setConsumeThreadMin(logisticsConfig.getLogisticsConsumeThreadNums());
        logisticsMQConsumer.shutdown();
        return logisticsMQConsumer;
    }


    @Bean(destroyMethod = "", name = "logisticsOrderMQConsumer")
    public DefaultMQPushConsumer logisticsOrderRocketMqConsumer() {
        logisticsOrderMQConsumer = new DefaultMQPushConsumer(rocketMQLogisticsOrderConfig.getConsumerId());
        logisticsOrderMQConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsOrderMQConsumer.setConsumeThreadMax(rocketMQLogisticsOrderConfig.getMaxThreadNum());
        logisticsOrderMQConsumer.setConsumeThreadMin(rocketMQLogisticsOrderConfig.getMaxThreadNum());
        logisticsOrderMQConsumer.shutdown();
        return logisticsOrderMQConsumer;
    }

    @Bean
    public RocketMqLifeCycleManager rocketMqLifeCycleManager() {
        return new RocketMqLifeCycleManager();
    }

    /**
     * Ons 生命周期管理
     *
     * <AUTHOR>
     * @date 2018/11/9
     */
    @Setter
    public static class RocketMqLifeCycleManager implements CommandLineRunner {
        private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqLifeCycleManager.class);

        @Autowired
        @Qualifier("smsDefaultProducer")
        private DefaultMQProducer smsProducer;

        @Autowired
        @Qualifier("logisticsMQConsumer")
        private DefaultMQPushConsumer logisticsRocketMqConsumer;

        @Autowired
        @Qualifier("logisticsOrderMQConsumer")
        private DefaultMQPushConsumer logisticsOrderRocketMqConsumer;

        @Autowired
        private LogisticConsumer logisticConsumer;

        @Autowired
        private LogisticsOrderConsumer logisticsOrderConsumer;

        @Autowired
        private LogisticsConfig logisticsConfig;

        @Autowired
        private RocketMQLogisticsOrderConfig rocketMQLogisticsOrderConfig;

        @Override
        public void run(String... args) throws Exception {
            // 启动物流短信ONS生产者
            if (smsProducer != null) {
                smsProducer.start();
                LOGGER.logInfo("Sms Producer startted");
            }
            // 启动物流轨迹ONS消费者
            if (logisticsRocketMqConsumer != null) {
                logisticsRocketMqConsumer.subscribe(logisticsConfig.getTopic(), "*");
                logisticsRocketMqConsumer.setMessageListener(logisticConsumer);
                logisticsRocketMqConsumer.start();
                LOGGER.logInfo("Logistic RocketMq Consumer is started, topic:" + logisticsConfig.getTopic());
            }

            // 启动物流单ONS消费者
            if (logisticsOrderRocketMqConsumer != null) {
                logisticsOrderRocketMqConsumer.subscribe(rocketMQLogisticsOrderConfig.getTopic(), "*");
                logisticsOrderRocketMqConsumer.setMessageListener(logisticsOrderConsumer);
                logisticsOrderRocketMqConsumer.start();
                LOGGER.logInfo("LogisticOrder RocketMq Consumer is started, topic:" + rocketMQLogisticsOrderConfig.getTopic());
            }
        }

        @PreDestroy
        public void preClose() {
            LOGGER.logInfo("正在关闭物流单 RocketMq Consumer...");
            if (logisticsOrderRocketMqConsumer != null) {
                try {
                    logisticsOrderRocketMqConsumer.suspend();
                    logisticsOrderRocketMqConsumer.unsubscribe(rocketMQLogisticsOrderConfig.getTopic());
                    logisticsOrderRocketMqConsumer.shutdown();
                } catch (IllegalStateException e) {
                } catch (Exception e) {
                    LOGGER.logError(e.getMessage(), e);
                }
            }
            LOGGER.logInfo("物流单 RocketMq Consumer已关闭, 稍后后清理消息");
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
            }
            LOGGER.logInfo("正在关闭物流轨迹 RocketMq Consumer...");
            if (logisticsRocketMqConsumer != null) {
                try {
                    logisticsRocketMqConsumer.suspend();
                    logisticsRocketMqConsumer.unsubscribe(logisticsConfig.getTopic());
                    logisticsRocketMqConsumer.shutdown();
                } catch (IllegalStateException e) {
                } catch (Exception e) {
                    LOGGER.logError(e.getMessage(), e);
                }
            }
            LOGGER.logInfo("物流轨迹 RocketMq Consumer已关闭, 稍后后清理消息");
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
            }
            logisticConsumer.stop();
            LOGGER.logInfo("正在关闭短信 RocketMq Producer...");
            if (smsProducer != null) {
                try {
                    smsProducer.shutdown();
                } catch (IllegalStateException e) {
                } catch (Exception e) {
                    LOGGER.logError(e.getMessage(), e);
                }
            }
            LOGGER.logInfo("短信 RocketMq Producer已关闭");

        }
    }

}

// package cn.loveapp.logistics.onsconsumer.task;
//
// import cn.loveapp.common.utils.LoggerHelper;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsInfoDTO;
// import cn.loveapp.logistics.onsconsumer.dto.LogisticsStatus;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsErrorPackage;
// import cn.loveapp.logistics.onsconsumer.entity.LogisticsPackageListen;
// import cn.loveapp.logistics.onsconsumer.entity.TradeLogisticsRule;
// import cn.loveapp.logistics.onsconsumer.service.LogisticsService;
// import cn.loveapp.logistics.onsconsumer.service.TaobaoService;
// import com.google.common.collect.Collections2;
// import com.google.common.util.concurrent.ThreadFactoryBuilder;
// import io.micrometer.core.annotation.Timed;
// import io.micrometer.core.instrument.MeterRegistry;
// import io.micrometer.core.instrument.Timer;
// import lombok.Setter;
// import org.apache.commons.lang3.tuple.Pair;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.beans.factory.annotation.Value;
// import org.springframework.context.ApplicationListener;
// import org.springframework.context.event.ContextClosedEvent;
// import org.springframework.dao.DataAccessException;
// import org.springframework.data.domain.PageRequest;
// import org.springframework.scheduling.annotation.Scheduled;
// import org.springframework.stereotype.Component;
// import org.springframework.util.CollectionUtils;
//
// import javax.annotation.PostConstruct;
// import java.util.*;
// import java.util.concurrent.*;
// import java.util.function.BooleanSupplier;
//
/// **
// * 物流包裹异常监控任务
// *
// * <AUTHOR>
// * @date 2018/10/12
// */
// @Component
// public class LogisticsPackageMonitoringJob implements ApplicationListener<ContextClosedEvent> {
// private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsPackageMonitoringJob.class);
//
// /**
// * 分表数量
// */
// protected static final int TABLE_COUNT = 1000;
//
// @Autowired
// private LogisticsService logisticsService;
//
// @Autowired
// private TaobaoService taobaoService;
//
// /**
// * 是否启动异常物流监控
// */
// @Value("${logistics.monitoring.enable:true}")
// private volatile boolean enableJob;
//
// /**
// * 处理异常物流的线程池大小
// */
// @Value("${logistics.monitoring.pool-size:10}")
// private int threadPoolSize;
//
// /**
// * 读取异常物流监控表的分页大小
// */
// @Value("${logistics.monitoring.page-size:5000}")
// private int pageSize;
//
// /**
// * 处理异常物流的线程池
// */
// private ThreadPoolExecutor threadPoolExecutor = null;
//
// private Timer timer;
//
// public LogisticsPackageMonitoringJob(MeterRegistry registry){
// timer = registry.timer("异常物流监控.QPS");
// }
//
// @PostConstruct
// protected void init(){
// threadPoolExecutor = new ThreadPoolExecutor(threadPoolSize, threadPoolSize, 5L, TimeUnit.SECONDS, new
// SynchronousQueue(),
// new ThreadFactoryBuilder().setNameFormat("package-monitoring-pool-%d").build());
// }
//
// protected ThreadPoolExecutor getExecutor(){
// return threadPoolExecutor;
// }
//
// protected void setEnableJob(boolean enableJob) {
// this.enableJob = enableJob;
// }
//
// protected boolean getEnableJob() {
// return this.enableJob;
// }
//
// protected Timer getTimer() {
// return timer;
// }
//
// @Override
// public void onApplicationEvent(ContextClosedEvent event) {
// if(event.getApplicationContext() !=null && event.getApplicationContext().getParent() != null){
// return;
// }
// LOGGER.logInfo("异常物流监控任务 正在关闭...");
// enableJob = false;
// threadPoolExecutor.shutdown();
// try {
// while(!threadPoolExecutor.awaitTermination(1, TimeUnit.SECONDS)){
// }
// } catch (InterruptedException e) {
// }
// LOGGER.logInfo("异常物流监控任务 已关闭.");
// }
//
// @Scheduled(cron = "${logistics.monitoring.cron:0 * * * * ?}")
// public void run() {
// if(!enableJob){
// return;
// }
//
// LOGGER.logInfo("异常物流监控任务 执行开始");
// try {
//
// //任务分片
// //每线程负责的分表数量
// int units = TABLE_COUNT / threadPoolSize;
// //线程负责的分表结束数
// int end = units - 1;
// //线程负责的分表起始数
// int start = 0;
//
// List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
// while (end < TABLE_COUNT && enableJob) {
//
// completableFutures.add(CompletableFuture.runAsync(createRunnable(end, start),
// threadPoolExecutor));
//
// start += units;
// end += units;
// }
// CompletableFuture<Void> all = CompletableFuture.allOf(completableFutures.toArray(new
// CompletableFuture[completableFutures.size()]));
// //等待所有任务结束
// while(true){
// try {
// all.get(2, TimeUnit.SECONDS);
// break;
// } catch (TimeoutException e) {
// }
// }
// } catch (Exception e) {
// LOGGER.logError(e.getMessage(), e);
// }
// LOGGER.logInfo("异常物流监控任务 执行结束");
// }
//
// protected PackageMonitoringRunner createRunnable(int end, int start) {
// return new PackageMonitoringRunner(timer, logisticsService, taobaoService, start, end, pageSize,
// ()->!this.enableJob);
// }
//
// /**
// * 异常物流探测线程
// *
// * <AUTHOR>
// * @date 2018年10月16日 上午10:42:14
// */
// @Setter
// protected static class PackageMonitoringRunner implements Runnable {
// private LogisticsService logisticsService;
// private TaobaoService taobaoService;
// private Timer timer;
// private int start;
// private int end;
// private int pageSize;
// private BooleanSupplier stopSupplier;
//
//
// /**
// * @param start 起始表
// * @param end 结束表
// * @param pageSize 分页大小
// * @param stopSupplier 是否停止监听的判断
// */
// public PackageMonitoringRunner(Timer timer, LogisticsService logisticsService, TaobaoService taobaoService,
// int start, int end, int pageSize, BooleanSupplier stopSupplier) {
// this.logisticsService = logisticsService;
// this.taobaoService = taobaoService;
// this.start = start;
// this.end = end;
// this.pageSize = pageSize;
// this.stopSupplier = stopSupplier;
// this.timer = timer;
// }
//
// @Override
// @Timed
// public void run() {
// for (int partitionId = start; partitionId < end; partitionId++) {
// LOGGER.logInfo(String.format("异常物流监控表-%d start", partitionId));
// Timer.Sample sample = Timer.start();
// try {
// for (int page = 0; ; page++) {
// if(stopSupplier.getAsBoolean()){
// return;
// }
// List<LogisticsPackageListen> result = logisticsService.findAllFromPackageListen(LogisticsPackageListen.REMARK_LISTEN,
// partitionId, PageRequest.of(page, pageSize));
//
// LOGGER.logInfo(String
// .format("异常物流监控表-%d page: %d pageSize: %d 数据: %d", partitionId, page, pageSize,
// result == null ? 0 : result.size()));
//
// if (result ==null || result.isEmpty()) {
// //无数据
// break;
// }
//
// for (LogisticsPackageListen logisticPackage : result) {
// if(stopSupplier.getAsBoolean()){
// return;
// }
// errorPackageDetection(logisticPackage, partitionId);
// }
//
// if(result.size() < pageSize){
// //数据已读取完
// break;
// }
// if(stopSupplier.getAsBoolean()){
// return;
// }
// }
// } catch (Exception e) {
// LOGGER.logError(String.format("异常物流监控表-%d 读取数据失败", partitionId), e);
// } finally {
// sample.stop(timer);
// }
// LOGGER.logInfo(String.format("异常物流监控表-%d end", partitionId));
// }
// }
//
// /**
// * 异常探测
// *
// * <AUTHOR>
// * @date 2018年10月16日 上午10:44:06
// * @param logisticPackage 要探测的包裹信息
// * @param partitionId 分库信息
// * @throws DataAccessException
// */
// protected void errorPackageDetection(LogisticsPackageListen logisticPackage, int partitionId)
// throws DataAccessException {
// String nick = logisticPackage.getSellerNick();
// String tid = logisticPackage.getTid();
// if(!taobaoService.getVipflag(nick)){
// //非vip用户, 不监控异常物流
// logisticsService.updatePackageListenRemark(nick, tid, logisticPackage.getOutSid(),
// LogisticsPackageListen.REMARK_CLOSE, partitionId);
// return;
// }
// LogisticsInfoDTO logisticsInfo = LogisticsInfoDTO.of(logisticPackage);
//
// String statusField = TradeLogisticsRule.getStatusField(logisticsInfo.getStatus());
// //遍历出全部规则中的最小天数的规则
// Pair<TradeLogisticsRule, String> ruleAndReceiverState = logisticsService
// .getMinStatusTimeErrorRule(statusField, logisticsInfo);
//
// TradeLogisticsRule minStatusTimeRule = ruleAndReceiverState.getLeft();
//
// if (minStatusTimeRule != null) {
// int interval = minStatusTimeRule.getStatusFieldValue(statusField);
// Date modifiedTime = logisticPackage.getModified();
// Date expectedEndTime = logisticPackage.getEndTime();
// Date endTime = logisticsService.getLogisticsRuleEndTime(modifiedTime, interval);
//
// //重新抓取规则后的时间 大于 记录的时间的话，更新最新的截止时间
// if (endTime.before(expectedEndTime)) {
// String errorReason = minStatusTimeRule.getStatusErrorReason(statusField);
// try {
// //异常包裹库
// logisticsService.saveErrorPackage(logisticsInfo, errorReason, LogisticsErrorPackage.TYPE_RULE);
// } catch (Exception e) {
// LOGGER.logError(nick, tid, "操作异常表时，出错", e);
// }
// }
// }
// }
// }
// }

package cn.loveapp.logistics.onsconsumer.service.impl;

import java.io.IOException;
import java.net.URLEncoder;

import javax.cache.annotation.CacheDefaults;
import javax.cache.annotation.CacheResult;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.common.utils.NetworkUtil;
import cn.loveapp.logistics.onsconsumer.dao.dream.UserProductinfoTradeDao;
import cn.loveapp.logistics.onsconsumer.entity.UserProductinfoTrade;
import cn.loveapp.logistics.onsconsumer.service.TaobaoService;

/**
 * TaobaoService
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
@CacheDefaults(cacheName = "runCache")
@Component
public class TaobaoServiceImpl implements TaobaoService {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(TaobaoService.class);
    protected static final String VIPFLAG = "vipflag";
    protected static final String TAOBAO_USER_ID = "taobao_user_id";

    @Value("${logistics.redis.rebuilduser.enable:false}")
    protected boolean enableRedisRebuildUserUrl;

    @Value("${logistics.redis.rebuilduser.url:}")
    private String redisRebuildUserUrl;

    @Autowired
    private StringRedisTemplate redisHelper;

    @Autowired
    private UserProductinfoTradeDao userProductinfoTradeDao;

    @Override
    @CacheResult
    public boolean getVipflag(String nick) {
        int retryCount = 3;
        for (int retry = 0; retry < retryCount; retry++) {
            try {
                String svipflag = getVipflagFromRedis(nick);
                if (!StringUtils.isEmpty(svipflag)) {
                    // redis获取成功
                    return "1".equals(svipflag);
                }
                if (!enableRedisRebuildUserUrl || StringUtils.isEmpty(redisRebuildUserUrl)) {
                    break;
                }
            } catch (Exception e) {
                LOGGER.logError(nick, "-", "连接Redis出现错误 ", e);
            }
        }
        // Redis连不上，尝试从数据库里面取得
        return this.getVipflagFromDB(nick);
    }

    /**
     * 根据nick从Redis中获取用户是否是高级版用户, redis中没有数据时重建redis用户信息, 再尝试读取
     *
     * @param nick
     *            卖家昵称
     * @return vipflag
     */
    protected String getVipflagFromRedis(String nick) throws Exception {
        HashOperations<String, String, String> op = redisHelper.opsForHash();
        String key = URLEncoder.encode(nick, "utf-8");
        String vipflag = op.get(key, VIPFLAG);
        if (StringUtils.isEmpty(vipflag) && enableRedisRebuildUserUrl && !StringUtils.isEmpty(redisRebuildUserUrl)) {
            rebuild(nick);
            // 再次读取
            vipflag = op.get(key, VIPFLAG);
        }
        return vipflag;
    }

    private void rebuild(String nick) throws IOException {
        if (!StringUtils.isEmpty(redisRebuildUserUrl)) {
            // 重建用户Redis信息
            String url = redisRebuildUserUrl + "?nick=" + nick;
            http(url);
            LOGGER.logInfo(nick, "-", "重建用户Redis信息");
        }
    }

    protected void http(String url) throws IOException {
        NetworkUtil.http(url, null, false, null, null, false, false, null);
    }

    /**
     * 从书库汇总取出用户类型，准备返回tag
     *
     * @param nick
     *            卖家nick
     * @return 是否是高级版用户
     */
    protected boolean getVipflagFromDB(String nick) {
        try {
            UserProductinfoTrade user = userProductinfoTradeDao.queryUserIdAndVipflagByNick(nick);
            if (user != null && user.getVipflag() == 1) {
                return true;
            }
        } catch (DataAccessException e) {
            LOGGER.logError(nick, "-", "连接数据库出现错误 ", e);
        }
        return false;
    }

    @Override
    public int getPartitionId(String corpId) {
        int partitionLength = 3;
        if (corpId.length() > partitionLength) {
            String partitionId = corpId.substring(corpId.length() - partitionLength);
            // 处理0开头的情况
            return Integer.parseInt(partitionId);
        } else {
            return Integer.parseInt(corpId);
        }
    }

    @Override
    public String getUserId(String nick, String tid) {
        try {
            try {
                HashOperations<String, String, String> op = redisHelper.opsForHash();
                String key = URLEncoder.encode(nick, "utf-8");
                String userIdStr = op.get(key, TAOBAO_USER_ID);
                if (!StringUtils.isEmpty(userIdStr)) {
                    return userIdStr;
                }
                rebuild(nick);
                userIdStr = op.get(key, TAOBAO_USER_ID);
                if (!StringUtils.isEmpty(userIdStr)) {
                    return userIdStr;
                }
            } catch (Exception e) {
                LOGGER.logError(nick, "-", "redis获取sellerId失败: " + e.getMessage(), e);
            }
            UserProductinfoTrade user = userProductinfoTradeDao.queryUserIdAndVipflagByNick(nick);
            if (user != null && user.getUserId() != null) {
                return String.valueOf(user.getUserId());
            } else {
                LOGGER.logError(nick, tid, "数据库中不存在user_id");
                return EMPTY_USER_ID;
            }
        } catch (DataAccessException e) {
            LOGGER.logError(nick, tid, "获取user_id连接数据库失败", e);
            return EMPTY_USER_ID;
        }
    }
}

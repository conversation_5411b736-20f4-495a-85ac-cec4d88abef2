package cn.loveapp.logistics.onsconsumer.consumer;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.common.constant.CommonPlatformConstants;
import cn.loveapp.common.serialization.MessageDeserializationResult;
import cn.loveapp.common.mq.AbstractCommonMQBaseConsumer;
import cn.loveapp.common.utils.LoggerHelper;
import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import cn.loveapp.logistics.api.dto.proto.LogisticsTraceRequestProto;
import cn.loveapp.logistics.common.service.LogisticsSendHandleService;
import cn.loveapp.logistics.common.service.UserCenterService;
import cn.loveapp.logistics.common.utils.LogisticsUtil;
import cn.loveapp.logistics.onsconsumer.service.platform.biz.LogisticsService;
import io.micrometer.core.instrument.MeterRegistry;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * LogsticConsumer
 *
 * <AUTHOR>
 * @date 2018/9/21
 */
@Component
public class LogisticConsumer extends AbstractCommonMQBaseConsumer {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticConsumer.class);

    /**
     * ONS限流配置Key
     */
    protected static final String LIMIT_CONFIG_KEY = "logistics.taobao.ons.ratelimit";

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsSendHandleService logisticsSendHandleService;

    @Autowired
    private UserCenterService userCenterService;


    public LogisticConsumer(MeterRegistry registry, Environment environment) {
        super(registry, "物流轨迹消息消费.QPS", LIMIT_CONFIG_KEY,
            environment, false, true);
    }


    public void stop() {
        LOGGER.logInfo("物流ONS消息处理关闭.");
        CommonLogisticsConstants.getPlatform().forEach(appName -> logisticsService.stop(appName, CommonAppConstants.APP_LOGISTICS));
    }

    @Override
    protected ConsumeConcurrentlyStatus execute(MessageExt message, MessageDeserializationResult messageDeserializationResult) throws IOException {
        LogisticsTraceRequestProto oMsg = (LogisticsTraceRequestProto) messageDeserializationResult.getContent();
        List<LogisticsInfoDTO> logisticsInfos = oMsg.getNotifyLogistics();
        String logisticsStoreId = oMsg.getLogisticsStoreId();
        String logisticsAppName = oMsg.getLogisticsAppName();
        boolean checkModified = BooleanUtils.isNotFalse(oMsg.isCheckModified());
        if (CollectionUtils.isEmpty(logisticsInfos)) {
            LOGGER.logError("缺少notify_logistics参数无法被解析");
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        Boolean isAbnormalAutoCheckUser = null;
        LogisticsInfoDTO logisticsInfoDTO = null;
        if (CollectionUtils.isNotEmpty(logisticsInfos)) {
            logisticsInfoDTO = logisticsInfos.get(0);
            String platformId = logisticsInfoDTO.getPlatformId();
            String sellerId = logisticsInfoDTO.getSellerId();
            String appName = logisticsInfoDTO.getAppName();
            String sellerNick = logisticsInfoDTO.getSellerNick();
            // 打标用户才走新入库逻辑（单条入库并且校验异常项）
            isAbnormalAutoCheckUser = isAbnormalAutoCheckUser(sellerId, sellerNick, platformId, appName);
        }

        if (StringUtils.isAnyEmpty(logisticsStoreId, logisticsAppName) && !Objects.isNull(logisticsInfoDTO)) {
            if (isAbnormalAutoCheckUser) {
                logisticsStoreId = logisticsInfoDTO.getPlatformId();
            } else {
                // 默认逻辑，批量入库类型
                logisticsStoreId = CommonPlatformConstants.PLATFORM_DEFAULT;
            }
            logisticsAppName = CommonAppConstants.APP_LOGISTICS;
        }

        // 灰度转发
        if (logisticsSendHandleService.pushLogisticsPretest(oMsg, logisticsInfos, message.getTopic())) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        logisticsService.saveLogisticInfo(logisticsInfos, oMsg, isAbnormalAutoCheckUser, checkModified, logisticsStoreId, logisticsAppName);

        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }

    /**
     * 判断是否多店用户
     *
     * @param sellerId
     * @param sellerNick
     * @param platformId
     * @param appName
     * @return
     */
    private boolean isAbnormalAutoCheckUser(String sellerId, String sellerNick, String platformId, String appName) {
        if (StringUtils.isAnyEmpty(appName, sellerId, platformId)) {
            return false;
        }

        if (LogisticsUtil.isTradeERP(appName)) {
            return true;
        }

        return userCenterService.isAbnormalAutoCheckUser(sellerId, sellerNick, platformId, appName);
    }

    @Override
    protected Class<?> getClassForDeserialization(MessageExt msg) {
        return LogisticsTraceRequestProto.class;
    }

}

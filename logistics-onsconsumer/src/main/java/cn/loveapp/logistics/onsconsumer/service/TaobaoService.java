package cn.loveapp.logistics.onsconsumer.service;

/**
 * TaobaoService
 *
 * <AUTHOR>
 * @date 2018/12/3
 */
public interface TaobaoService {
    String EMPTY_USER_ID = "0";

    /**
     * 根据nick从Redis中获取用户是否是高级版用户，如果Redis中不存在，重建redis, 再次读取, 重试redis 3次都失败后从数据库中获取
     *
     * @param nick
     *            卖家昵称
     * @return vipflag
     */
    boolean getVipflag(String nick);

    /**
     * 根据corpId获得分区情况
     *
     * @param corpId
     *            corpId
     * @return
     */
    int getPartitionId(String corpId);

    /**
     * 获取user_id
     *
     * @param nick
     *            卖家昵称
     * @param tid
     *            订单id
     * @return
     */
    String getUserId(String nick, String tid);
}

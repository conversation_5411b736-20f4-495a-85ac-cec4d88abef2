<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>cn.loveapp.common</groupId>
        <artifactId>common-spring-boot-parent</artifactId>
        <version>1.35.21-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <name>爱用宝基础服务-物流服务-二方API接口结构</name>
    <description>爱用宝基础服务-物流服务-二方API接口结构</description>
    <groupId>cn.loveapp.logistics</groupId>
    <artifactId>logistics-api</artifactId>
    <version>1.0.7-SNAPSHOT</version>
    <packaging>jar</packaging>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <properties>
    </properties>
    <dependencies>
        <dependency>
            <groupId>cn.loveapp.common</groupId>
            <artifactId>common-spring-boot-web-starter</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-starter-openfeign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.protostuff</groupId>
            <artifactId>protostuff-runtime</artifactId>
        </dependency>
    </dependencies>

</project>

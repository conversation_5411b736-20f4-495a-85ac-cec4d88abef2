package cn.loveapp.logistics.api.dto.proto;

import cn.loveapp.logistics.api.dto.LogisticsInfoDTO;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;

import io.protostuff.Tag;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;

/**
 * LogisticsTraceRequestProto
 *
 * <AUTHOR>
 * @date 2022/1/23
 */
@Data
public class LogisticsTraceRequestProto {

    @Tag(1)
    @JSONField(name = "notify_logistics")
    private List<LogisticsInfoDTO> notifyLogistics;

    /**
     * 物流平台
     */
    @Tag(2)
    @JSONField(name = "logistics_store_id")
    private String logisticsStoreId;

    /**
     * 物流应用
     */
    @Tag(3)
    @JSONField(name = "logistics_app_name")
    private String logisticsAppName;

    /**
     * 是否比较Modified
     */
    @Tag(4)
    @JSONField(name = "check_modified")
    private boolean checkModified = true;

    /**
     * 转换为 JSONObject
     * @param logisticsTraceRequestProto
     * @return
     */
    public static JSONObject toOriginalJson(LogisticsTraceRequestProto logisticsTraceRequestProto) {
        HashMap<String, Object> stringObjectHashMap = new HashMap<>();
        List<LogisticsInfoDTO> notifyLogistics = logisticsTraceRequestProto.getNotifyLogistics();
        stringObjectHashMap.put("notify_logistics", (notifyLogistics.size() == 1 ? notifyLogistics.get(0) : notifyLogistics));
        stringObjectHashMap.put("logistics_store_id", logisticsTraceRequestProto.getLogisticsStoreId());
        stringObjectHashMap.put("logistics_app_name", logisticsTraceRequestProto.getLogisticsAppName());
        stringObjectHashMap.put("check_modified", logisticsTraceRequestProto.isCheckModified());
        return (JSONObject) JSONObject.toJSON(stringObjectHashMap);
    }

    /**
     * JSONObject 转换为 LogisticsMessageDTO
     * @param jsonObject
     * @return
     */
    public static LogisticsTraceRequestProto fromJsonObject(JSONObject jsonObject) {
        LogisticsTraceRequestProto logisticsTraceRequestProto = new LogisticsTraceRequestProto();
        // 仅单条
        LogisticsInfoDTO logisticsInfoDTO = LogisticsInfoDTO.fromJsonObject((JSONObject) jsonObject.get("notify_logistics"));
        Boolean checkModified = jsonObject.getBoolean("check_modified");
        logisticsTraceRequestProto.setNotifyLogistics(Collections.singletonList(logisticsInfoDTO));
        logisticsTraceRequestProto.setLogisticsStoreId(jsonObject.getString("logistics_store_id"));
        logisticsTraceRequestProto.setLogisticsAppName(jsonObject.getString("logistics_app_name"));
        logisticsTraceRequestProto.setCheckModified(BooleanUtils.isNotFalse(checkModified));

        return logisticsTraceRequestProto;
    }
}

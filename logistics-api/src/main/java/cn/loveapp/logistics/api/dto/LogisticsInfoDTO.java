package cn.loveapp.logistics.api.dto;

import cn.loveapp.common.utils.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import io.protostuff.Tag;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Map;

/**
 * ONS通知的物流信息内容
 *
 * <AUTHOR>
 * @date 2018/11/27
 */
@Data
public class LogisticsInfoDTO {

    /**
     * 卖家nick
     */
    @Tag(1)
    @JSONField(name = "nick")
    private String sellerNick;

    @Tag(2)
    @JSONField(name = "seller_nick")
    private String sellerNickV2;
    /**
     * 订单id
     */
    @Tag(3)
    private String tid;

    /**
     * 卖家id
     */
    @Tag(4)
    @JSONField(name = "user_id")
    private String sellerId;

    @Tag(5)
    @JSONField(name = "seller_id")
    private String sellerIdV2;

    /**
     * 物流单号
     */
    @Tag(6)
    @JSONField(name = "out_sid")
    private String outSid;

    /**
     * 物流动作
     */
    @Tag(7)
    private String action;

    /**
     * 更新时间
     */
    @Tag(8)
    @JSONField(name = "time")
    private Date modified;

    @Tag(9)
    @JSONField(name = "modified")
    private Date modifiedV2;

    /**
     * 物流状态描述
     */
    @Tag(10)
    private String desc;

    /**
     * 快递公司
     */
    @Tag(11)
    @JSONField(name = "company_name")
    private String companyName;

    /**
     * 快递公司
     */
    @Tag(12)
    @JSONField(name = "company_code")
    private String companyCode;

    /**
     * 目的省份
     */
    @Tag(13)
    private String province;


    /**
     * 应用名称 如 guanDian
     */
    @Tag(14)
    @JSONField(name = "app_name")
    private String appName;

    @Tag(15)
    @JSONField(name = "appName")
    private String appNameV2;

    /**
     * 平台 如 TAO PDD
     */
    @Tag(16)
    @JSONField(name = "platform_id")
    private String platformId;

    /**
     * 物流轨迹平台（快递鸟、拼多多、淘宝）
     */
    @Tag(17)
    @JSONField(name = "logistics_store_id")
    private String logisticsStoreId;

    /**
     * 是否需要重新订阅
     */
    @Tag(18)
    @JSONField(name = "is_need_subscribe")
    private Boolean isNeedReSubscribe;

    /**
     * 多平台拉平的物流轨迹状态
     */
    @Tag(19)
    private String status;

    @Tag(20)
    @JSONField(name = "store_id")
    private String storeId;

    @Tag(21)
    @JSONField(name = "tp_code")
    private String tpCode;

    @Tag(22)
    @JSONField(name = "topic")
    private String topic;

    @Tag(23)
    @JSONField(name = "tag")
    private String tag;

    @Tag(24)
    @JSONField(name = "reason")
    private String reason;

    @Tag(25)
    @JSONField(name = "remark")
    private String remark;

    public LogisticsInfoDTO() {

    }

    public void setCompanyName(String companyName) {
        if (companyName == null) {
            this.companyName = StringUtils.EMPTY;
        } else {
            this.companyName = companyName;
        }
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }


    public void setModified(Date time) {
        this.modified = time;
    }

    public void setModifiedV2(Date modified) {
        this.modifiedV2 = modified;
    }

    /**
     * string format: "yyyy-MM-dd HH:mm:ss"
     * @param time
     */
    public void setModified(String time) {
        if (StringUtils.isNotEmpty(time)) {
            Date date = DateUtil.parseDateString(time);
            this.modified = date;
        }
    }

    /**
     * string format: "yyyy-MM-dd HH:mm:ss"
     * @param modified
     */
    public void setModifiedV2(String modified) {
        if (StringUtils.isNotEmpty(modified)) {
            Date date = DateUtil.parseDateString(modified);
            this.modifiedV2 = date;
        }
    }

    /**
     * JSONObject 转换为对应的 LogisticsInfoDTO
     * @param jsonObject
     * @return
     */
    public static LogisticsInfoDTO fromJsonObject(JSONObject jsonObject) {
        LogisticsInfoDTO logisticsInfoDTO = new LogisticsInfoDTO();
        logisticsInfoDTO.setSellerNick(jsonObject.getString("nick"));
        logisticsInfoDTO.setSellerNickV2(jsonObject.getString("seller_nick"));
        logisticsInfoDTO.setTid(jsonObject.getString("tid"));
        logisticsInfoDTO.setSellerId(jsonObject.getString("user_id"));
        logisticsInfoDTO.setSellerIdV2(jsonObject.getString("seller_id"));
        logisticsInfoDTO.setOutSid(jsonObject.getString("out_sid"));
        logisticsInfoDTO.setAction(jsonObject.getString("action"));
        logisticsInfoDTO.setModified(jsonObject.getDate("time"));
        logisticsInfoDTO.setModifiedV2(jsonObject.getDate("modified"));
        logisticsInfoDTO.setDesc(jsonObject.getString("desc"));
        logisticsInfoDTO.setCompanyName(jsonObject.getString("company_name"));
        logisticsInfoDTO.setCompanyCode(jsonObject.getString("company_Code"));
        logisticsInfoDTO.setProvince(jsonObject.getString("province"));
        logisticsInfoDTO.setAppName(jsonObject.getString("app_name"));
        logisticsInfoDTO.setAppNameV2(jsonObject.getString("appName"));
        logisticsInfoDTO.setPlatformId(jsonObject.getString("platform_id"));
        logisticsInfoDTO.setLogisticsStoreId(jsonObject.getString("logistics_store_id"));
        logisticsInfoDTO.setIsNeedReSubscribe(jsonObject.getBoolean("is_need_subscribe"));
        logisticsInfoDTO.setStatus(jsonObject.getString("status"));
        logisticsInfoDTO.setStoreId(jsonObject.getString("store_id"));
        logisticsInfoDTO.setTpCode(jsonObject.getString("tp_code"));
        logisticsInfoDTO.setTopic(jsonObject.getString("topic"));
        logisticsInfoDTO.setTag(jsonObject.getString("tag"));

        // remark、reason
        return logisticsInfoDTO;
    }

    /**
     * Map 转换为对应的 LogisticsInfoDTO
     * @param stringObjectMap
     * @return
     */
    public static LogisticsInfoDTO fromMap(Map<String, Object> stringObjectMap) {
        LogisticsInfoDTO logisticsInfoDTO = new LogisticsInfoDTO();
        logisticsInfoDTO.setSellerNick((String) stringObjectMap.get("nick"));
        logisticsInfoDTO.setSellerNickV2((String) stringObjectMap.get("seller_nick"));
        logisticsInfoDTO.setTid(String.valueOf(stringObjectMap.get("tid")));
        logisticsInfoDTO.setSellerId((String) stringObjectMap.get("user_id"));
        logisticsInfoDTO.setSellerIdV2((String) stringObjectMap.get("seller_id"));
        logisticsInfoDTO.setOutSid((String) stringObjectMap.get("out_sid"));
        logisticsInfoDTO.setAction((String) stringObjectMap.get("action"));
        logisticsInfoDTO.setModified((String) stringObjectMap.get("time"));
        logisticsInfoDTO.setModifiedV2((String) stringObjectMap.get("modified"));
        logisticsInfoDTO.setDesc((String) stringObjectMap.get("desc"));
        logisticsInfoDTO.setCompanyName((String) stringObjectMap.get("company_name"));
        logisticsInfoDTO.setCompanyCode((String) stringObjectMap.get("company_Code"));
        logisticsInfoDTO.setProvince((String) stringObjectMap.get("province"));
        logisticsInfoDTO.setAppName((String) stringObjectMap.get("app_name"));
        logisticsInfoDTO.setAppNameV2((String) stringObjectMap.get("appName"));
        logisticsInfoDTO.setPlatformId((String) stringObjectMap.get("platform_id"));
        logisticsInfoDTO.setLogisticsStoreId((String) stringObjectMap.get("logistics_store_id"));
        logisticsInfoDTO.setIsNeedReSubscribe((Boolean) stringObjectMap.get("is_need_subscribe"));
        logisticsInfoDTO.setStatus((String) stringObjectMap.get("status"));
        logisticsInfoDTO.setStoreId((String) stringObjectMap.get("store_id"));
        logisticsInfoDTO.setTpCode((String) stringObjectMap.get("tp_code"));
        logisticsInfoDTO.setTopic((String) stringObjectMap.get("topic"));
        logisticsInfoDTO.setTag((String) stringObjectMap.get("tag"));

        // remark、reason
        return logisticsInfoDTO;
    }
}

package cn.loveapp.logistics.api.dto;

import cn.loveapp.common.constant.CommonAppConstants;
import cn.loveapp.common.constant.CommonLogisticsConstants;
import cn.loveapp.logistics.api.constant.BusinessType;
import io.protostuff.Tag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 物流传输DTO
 *
 * <AUTHOR>
 * @Date 2023/5/30 16:36
 */
@Data
@ApiModel
public class LogisticsOrderSubscribeDTO {

    /**
     * 物流平台
     */
    @ApiModelProperty(value = "物流平台", required = true)
    @NotNull
    @Tag(1)
    private String logisticsStoreId = CommonLogisticsConstants.PLATFORM_KDNIAO;

    /**
     * 物流应用
     */
    @ApiModelProperty(value = "物流应用", required = true)
    @NotNull
    @Tag(2)
    private String logisticsAppName = CommonAppConstants.APP_LOGISTICS;

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull
    @Tag(3)
    private String sellerId;

    /**
     * 用户nick
     */
    @ApiModelProperty(value = "用户nick", required = true)
    @Tag(4)
    private String sellerNick;

    /**
     * 用户平台
     */
    @ApiModelProperty(value = "用户平台", required = true)
    @NotNull
    @Tag(5)
    private String storeId;

    /**
     * 应用
     */
    @ApiModelProperty(value = "应用", required = true)
    @NotNull
    @Tag(6)
    private String appName;

    /**
     * 平台来源
     */
    @Tag(7)
    private String sourceApp;


    /**
     * 业务类型
     */
    @Tag(8)
    @ApiModelProperty(value = "业务类型")
    private BusinessType businessType;

    /**
     * 业务类型id（根据业务类型区分）
     */
    @Tag(9)
    @ApiModelProperty(value = "业务id列表")
    private List<String> businessIds;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    @Tag(10)
    private String outSid;

    /**
     * 物流公司Code（物流平台对应物流公司code）
     */
    @ApiModelProperty(value = "物流公司Code")
    @Tag(11)
    private String logisticsCompanyCode;

    /**
     * 物流公司Code（物流平台对应物流公司Name）
     */
    @ApiModelProperty(value = "物流公司Name")
    @Tag(12)
    private String logisticsCompanyName;

    /**
     * 源物流公司（电商平台对应物流公司code/name/id）
     */
    @ApiModelProperty(value = "源物流公司（电商平台对应物流公司code/name/id）")
    @Tag(13)
    private String sourceLogisticsCompany;

    /**
     * 轨迹订阅是否入库
     */
    @ApiModelProperty(value = "轨迹订阅是否入库")
    @Tag(14)
    private boolean needSave = true;

    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @Tag(15)
    private Date consignTime;

    /**
     * 寄件人or收件人 手机号后四位,只有顺丰订阅快递鸟时才需要
     */
    @ApiModelProperty(value = "寄件人or收件人 手机号后四位,只有顺丰订阅快递鸟时才需要", required = false)
    @Tag(16)
    private String customerName;

    /**
     * 查询轨迹是否消耗物流包额度
     */
    @ApiModelProperty(value = "查询轨迹是否消耗物流包额度")
    @Tag(17)
    private boolean isSearchNeedDeductionQuota = true;

    /**
     * 订阅轨迹是否需要消耗物流包额度
     */
    @ApiModelProperty(value = "订阅轨迹是否需要消耗物流包额度")
    @Tag(18)
    private boolean isSubscribeNeedDeductionQuota = true;

    /**
     * 买家昵称
     */
    @ApiModelProperty(value = "买家昵称")
    @Tag(19)
    private String buyerNick;

    /**
     * 买家OpenUid
     */
    @ApiModelProperty(value = "买家OpenUid")
    @Tag(20)
    private String buyerOpenUid;

    /**
     * 物流平台（防止前段订阅物流时直接传code导致不走菜鸟物流映射逻辑的问题）
     */
    @ApiModelProperty(value = "物流原始平台")
    @NotNull
    @Tag(21)
    private String originalLogisticsStoreId;

    /**
     * 收件人/寄件人手机 顺丰必传
     */
    @ApiModelProperty(value = "收件人/寄件人手机 顺丰必传")
    @Tag(22)
    private String phone;

}

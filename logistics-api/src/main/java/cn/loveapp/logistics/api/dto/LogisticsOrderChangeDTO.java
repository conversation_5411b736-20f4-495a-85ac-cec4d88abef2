package cn.loveapp.logistics.api.dto;

import io.protostuff.Tag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 物流信息变更传输DTO
 *
 * <AUTHOR>
 * @Date 2023/6/25 17:18
 */
@Data
@ApiModel
public class LogisticsOrderChangeDTO {

    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id", required = true)
    @NotNull
    @Tag(1)
    private String sellerId;

    /**
     * 用户nick
     */
    @ApiModelProperty(value = "用户nick", required = true)
    @Tag(2)
    private String sellerNick;

    /**
     * 用户平台
     */
    @ApiModelProperty(value = "用户平台", required = true)
    @NotNull
    @Tag(3)
    private String storeId;

    /**
     * 应用
     */
    @ApiModelProperty(value = "应用", required = true)
    @NotNull
    @Tag(4)
    private String appName;

    /**
     * 运单号
     */
    @ApiModelProperty(value = "运单号", required = true)
    @NotNull
    @Tag(5)
    private String outSid;

    /**
     * 拦截标记
     */
    @ApiModelProperty(value = "拦截标记")
    @Tag(6)
    private Boolean isTagIntercepted;

    /**
     * 拦截标记时间
     */
    @ApiModelProperty(value = "拦截标记时间")
    @Tag(7)
    private Date tagInterceptedModified;

    /**
     * 处理状态
     */
    @ApiModelProperty(value = "处理状态")
    @Tag(8)
    private String processStatus;
}

package cn.loveapp.logistics.api.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 物流订阅Rpc接口Response
 *
 * <AUTHOR>
 * @Date 2023/5/31 18:30
 */
@ApiModel
@Data
public class LogisticsTraceSubscribeResponse {

    /**
     * 订阅结果列表
     */
    @ApiModelProperty(value = "订阅结果列表")
    private List<TraceSubscribeResult> traceSubscribeResults;

    @Data
    public static class TraceSubscribeResult {

        /**
         * 运单号
         */
        @ApiModelProperty(value = "运单号")
        @NotNull
        private String outSid;


        /**
         * 是否成功
         */
        @ApiModelProperty(value = "是否成功")
        private boolean success;


        /**
         * 错误信息
         */
        @ApiModelProperty(value = "错误信息")
        private String errorMessage;
    }

    public void addTraceSubscribeResult(TraceSubscribeResult result) {
        if (traceSubscribeResults == null) {
            traceSubscribeResults = new ArrayList<>();
        }
        traceSubscribeResults.add(result);
    }

}

package cn.loveapp.logistics.api.dto;

import java.util.List;

import io.protostuff.Tag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025-03-18 18:20
 * @description: 同步订单信息DTO
 */
@Data
public class OrderInfoChangeDTO {

    @ApiModelProperty(value = "用户id")
    @Tag(1)
    private String sellerId;

    @ApiModelProperty(value = "用户nick")
    @Tag(2)
    private String sellerNick;

    @ApiModelProperty(value = "平台")
    @Tag(3)
    private String storeId;

    @ApiModelProperty(value = "应用")
    @Tag(4)
    private String appName;

    @ApiModelProperty(value = "运单号列表")
    @Tag(5)
    private List<String> outSids;

    @ApiModelProperty(value = "订单信息")
    @Tag(6)
    private OrderInfoDTO orderInfo;

}

package cn.loveapp.logistics.api.dto.proto;

import cn.loveapp.logistics.api.dto.LogisticsOrderSubscribeDTO;
import cn.loveapp.logistics.api.dto.OrderInfoChangeDTO;
import cn.loveapp.logistics.api.dto.LogisticsOrderChangeDTO;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * 物流单消息传说request
 *
 * <AUTHOR>
 * @Date 2023/7/11 12:19
 */
@Data
public class LogisticsOrderRequestProto {

    /**
     * 物流列表
     */
    @JsonProperty("logistics_handles")
    @JSONField(name = "logistics_handles")
    @Tag(1)
    private List<LogisticsOrderSubscribeDTO> logisticsHandles;

    /**
     * 物流单打标
     */
    @JsonProperty("logistics_update_infos")
    @JSONField(name = "logistics_update_infos")
    @Tag(2)
    private List<LogisticsOrderChangeDTO> logisticsUpdateInfos;

    /**
     * 订单信息变更
     */
    @JsonProperty("order_info_change")
    @JSONField(name = "order_info_change")
    @Tag(3)
    private OrderInfoChangeDTO orderInfoChange;

}

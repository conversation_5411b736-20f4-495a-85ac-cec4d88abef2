<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>cn.loveapp.logistics</groupId>
    <artifactId>logistics-services-group</artifactId>
    <packaging>pom</packaging>
    <version>1.1-SNAPSHOT</version>
    <modules>
        <module>logistics-service</module>
        <module>logistics-api</module>
        <module>logistics-onsconsumer</module>
        <module>logistics-abnormal-consumer</module>
        <module>logistics-common</module>
    </modules>

    <parent>
        <groupId>cn.loveapp.common</groupId>
        <artifactId>common-spring-boot-parent</artifactId>
        <version>1.35.21-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <name>爱用-物流</name>
    <description>爱用-物流</description>

    <organization>
        <name>Loveapp Inc.</name>
        <url>http://www.aiyongbao.com</url>
    </organization>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <!--<spring-cloud.version>Dalston.SR4</spring-cloud.version>-->
        <pgbulkinsert.version>2.2</pgbulkinsert.version>
        <spymemcached.version>2.12.3</spymemcached.version>
        <contiperf.version>2.3.4</contiperf.version>
        <logstash-logback-encoder.version>6.3</logstash-logback-encoder.version>
        <snappy.version>*******</snappy.version>
        <uac-api.version>1.16.11-SNAPSHOT</uac-api.version>
        <logistics-api.version>1.0.7-SNAPSHOT</logistics-api.version>
        <orders-api.version>1.5.1-SNAPSHOT</orders-api.version>
        <rocketmq.version>4.5.0</rocketmq.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.databene</groupId>
            <artifactId>contiperf</artifactId>
            <version>${contiperf.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>
        <!-- mongo -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mongodb-driver</artifactId>
                    <groupId>org.mongodb</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-mongodb</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.mongodb</groupId>
                    <artifactId>mongo-java-driver</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mongodb</groupId>
            <artifactId>mongo-java-driver</artifactId>
        </dependency>
        <dependency>
            <groupId>org.xerial.snappy</groupId>
            <artifactId>snappy-java</artifactId>
            <version>${snappy.version}</version>
        </dependency>
        <!--<dependency>-->
        <!--&lt;!&ndash;  must be on the classpath  &ndash;&gt;-->
        <!--<groupId>org.jacoco</groupId>-->
        <!--<artifactId>org.jacoco.agent</artifactId>-->
        <!--<classifier>runtime</classifier>-->
        <!--<version>0.8.2</version>-->
        <!--<scope>test</scope>-->
        <!--</dependency>-->
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!--<dependency>-->
            <!--<groupId>org.springframework.cloud</groupId>-->
            <!--<artifactId>spring-cloud-starter-hsf</artifactId>-->
            <!--<version>${pandora.version}</version>-->
            <!--</dependency>-->
            <!--<dependency>-->
            <!--<groupId>org.springframework.cloud</groupId>-->
            <!--<artifactId>spring-cloud-starter-pandora</artifactId>-->
            <!--<version>${pandora.version}</version>-->
            <!--</dependency>-->
            <dependency>
                <groupId>cn.loveapp.uac</groupId>
                <artifactId>uac-api</artifactId>
                <version>${uac-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.loveapp.orders</groupId>
                <artifactId>orders-api</artifactId>
                <version>${orders-api.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.loveapp.common</groupId>
                        <artifactId>common-spring-boot-parent</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>net.spy</groupId>
                <artifactId>spymemcached</artifactId>
                <version>${spymemcached.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>

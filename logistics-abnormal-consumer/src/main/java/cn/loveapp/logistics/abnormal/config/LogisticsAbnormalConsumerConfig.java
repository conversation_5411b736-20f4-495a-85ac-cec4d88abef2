package cn.loveapp.logistics.abnormal.config;

import javax.annotation.PreDestroy;

import cn.loveapp.logistics.abnormal.consumer.LogisticsAbnormalConsumer;
import cn.loveapp.logistics.common.config.LogisticsConfig;
import cn.loveapp.logistics.common.config.rocketmq.RocketMQLogisticsAbnormalConfig;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import cn.loveapp.common.utils.LoggerHelper;
import lombok.Setter;

/**
 * LogisticsAbnormalConsumerConfig 异常物流队列消费配置
 *
 * <AUTHOR>
 * @date 2023/6/28
 */
@Configuration
public class LogisticsAbnormalConsumerConfig {
    private static final LoggerHelper LOGGER = LoggerHelper.getLogger(LogisticsAbnormalConsumerConfig.class);

    private DefaultMQPushConsumer logisticsConsumer = null;


    @Autowired
    private LogisticsConfig logisticsConfig;

    @Autowired
    private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

    @Bean(destroyMethod = "")
    public DefaultMQPushConsumer logisticsRocketMqConsumer() {
        logisticsConsumer = new DefaultMQPushConsumer(rocketMQLogisticsAbnormalConfig.getConsumerId());
        logisticsConsumer.setNamesrvAddr(logisticsConfig.getNamesrvAddr());
        logisticsConsumer.setConsumeThreadMax(rocketMQLogisticsAbnormalConfig.getMaxThreadNum());
        logisticsConsumer.setConsumeThreadMin(rocketMQLogisticsAbnormalConfig.getMaxThreadNum());
        // logisticsConsumer.setPullBatchSize(32);
        // logisticsConsumer.setConsumeMessageBatchMaxSize(32);
        logisticsConsumer.shutdown();
        return logisticsConsumer;
    }

    @Bean
    public RocketMqLifeCycleManager rocketMqLifeCycleManager() {
        return new RocketMqLifeCycleManager();
    }

    /**
     * Ons 生命周期管理
     *
     * <AUTHOR>
     * @date 2018/11/9
     */
    @Setter
    public static class RocketMqLifeCycleManager implements CommandLineRunner {
        private static final LoggerHelper LOGGER = LoggerHelper.getLogger(RocketMqLifeCycleManager.class);

        @Autowired
        private DefaultMQPushConsumer logisticsRocketMqConsumer;

        @Autowired
        private LogisticsAbnormalConsumer logisticConsumer;

        @Autowired
        private RocketMQLogisticsAbnormalConfig rocketMQLogisticsAbnormalConfig;

        @Override
        public void run(String... args) throws Exception {
            // 启动物流ONS消费者
            if (logisticsRocketMqConsumer != null) {
                logisticsRocketMqConsumer.subscribe(rocketMQLogisticsAbnormalConfig.getTopic(), "*");
                logisticsRocketMqConsumer.setMessageListener(logisticConsumer);
                logisticsRocketMqConsumer.start();
                LOGGER.logInfo("Logistic RocketMq Consumer is started, topic:" + rocketMQLogisticsAbnormalConfig.getTopic());
            }
        }

        @PreDestroy
        public void preClose() {
            LOGGER.logInfo("正在关闭物流异常队列 RocketMq Consumer...");
            if (logisticsRocketMqConsumer != null) {
                try {
                    logisticsRocketMqConsumer.suspend();
                    logisticsRocketMqConsumer.unsubscribe(rocketMQLogisticsAbnormalConfig.getTopic());
                    logisticsRocketMqConsumer.shutdown();
                } catch (IllegalStateException e) {
                } catch (Exception e) {
                    LOGGER.logError(e.getMessage(), e);
                }
            }
            LOGGER.logInfo("物流异常队列 RocketMq Consumer已关闭");

        }
    }

}

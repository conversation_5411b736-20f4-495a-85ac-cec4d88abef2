spring.application.name=logistics-abnormal
spring.profiles.active=dev
# \u662F\u5426\u5141\u8BB8apollo
loveapp.apollo.enabled=true
# apollo \u57FA\u7840\u914D\u7F6E
app.id=cn.loveapp.logistics
apollo.bootstrap.enabled=${loveapp.apollo.enabled}
# \u516C\u5171namespace\u5FC5\u987B\u653E\u540E\u9762
apollo.bootstrap.namespaces=logistics-abnormal,logistics-mongodb,application,service-registry
env=${spring.profiles.active}
# dubbo
# \u662F\u5426\u5141\u8BB8dubbo\u6D88\u8D39\u8005
dubbo.enabled=false
dubbo.application.name=logistics-abnormal
dubbo.scan.base-packages=cn.loveapp.logistics.abnormal
dubbo.consumer.check=false
spring.cache.jcache.config=classpath:ehcache.xml

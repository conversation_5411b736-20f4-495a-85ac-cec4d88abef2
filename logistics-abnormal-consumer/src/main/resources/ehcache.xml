<?xml version="1.0" encoding="UTF-8"?>
<config
    xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'
    xmlns='http://www.ehcache.org/v3'
    xsi:schemaLocation="http://www.ehcache.org/v3 http://www.ehcache.org/schema/ehcache-core.xsd">

    <cache-template name="default">
        <resources>
            <heap unit="entries">10000</heap>
        </resources>
    </cache-template>

    <cache alias="runCache" uses-template="default">
        <expiry>
            <ttl unit="seconds">300</ttl>
        </expiry>
    </cache>

    <cache alias="userCache" uses-template="default">
        <expiry>
            <ttl unit="hours">1</ttl>
        </expiry>
    </cache>

</config>
